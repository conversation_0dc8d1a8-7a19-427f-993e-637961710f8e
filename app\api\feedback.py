from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from typing import Optional, List
import uuid
import logging

from app.models.base import get_db
from app.models.user import User
from app.models.feedback import Feedback
from app.models.message import Message
from app.models.session import Session as SessionModel
from app.schemas.feedback import (
    FeedbackCreate,
    FeedbackUpdate,
    FeedbackResponse,
    FeedbackListResponse,
    FeedbackStatsResponse,
    FeedbackWithContext,
    MessageContext
)
from app.core.dependencies import get_current_user
from app.core.exceptions import NotFoundError, ConflictError, ValidationError

logger = logging.getLogger(__name__)

router = APIRouter()


def get_feedback_by_id(db: Session, feedback_id: str, user_id: uuid.UUID) -> Optional[Feedback]:
    """Get feedback by ID for a specific user."""
    try:
        feedback_uuid = uuid.UUID(feedback_id)
        return db.query(Feedback).filter(
            and_(Feedback.id == feedback_uuid, Feedback.user_id == user_id)
        ).first()
    except ValueError:
        return None


def get_message_by_id(db: Session, message_id: str, user_id: uuid.UUID) -> Optional[Message]:
    """Get message by ID and verify user has access to it."""
    try:
        message_uuid = uuid.UUID(message_id)
        return db.query(Message).join(SessionModel).filter(
            and_(
                Message.id == message_uuid,
                SessionModel.user_id == user_id
            )
        ).first()
    except ValueError:
        return None


def get_message_context(db: Session, message: Message, context_size: int = 5) -> tuple[Optional[Message], List[Message]]:
    """
    Get the user question that preceded this agent response and recent conversation context.

    Args:
        db: Database session
        message: The message being rated (should be an assistant message)
        context_size: Number of recent messages to include in context

    Returns:
        tuple: (user_question, conversation_context)
    """
    # Get recent messages in the same session, ordered by sequence number
    recent_messages = db.query(Message).filter(
        Message.session_id == message.session_id
    ).order_by(Message.sequence_number.desc()).limit(context_size * 2).all()

    # Reverse to get chronological order
    recent_messages.reverse()

    # Find the user question that immediately preceded this assistant message
    user_question = None
    for i, msg in enumerate(recent_messages):
        if msg.id == message.id and i > 0:
            # Look for the most recent user message before this assistant message
            for j in range(i - 1, -1, -1):
                if recent_messages[j].role == 'user':
                    user_question = recent_messages[j]
                    break
            break

    # Get conversation context (last few messages)
    conversation_context = recent_messages[-context_size:] if len(recent_messages) > context_size else recent_messages

    return user_question, conversation_context


def create_message_context(message: Message) -> MessageContext:
    """Convert a Message model to MessageContext schema."""
    return MessageContext(
        id=message.id,
        role=message.role,
        content=message.content,
        sequence_number=message.sequence_number,
        created_at=message.created_at
    )


@router.post("/", response_model=FeedbackResponse, status_code=status.HTTP_201_CREATED)
async def create_feedback(
    feedback_data: FeedbackCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create feedback for a message.
    
    Args:
        feedback_data: Feedback creation data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        FeedbackResponse: Created feedback
        
    Raises:
        NotFoundError: If message not found or user doesn't have access
        ConflictError: If feedback already exists for this message
        ValidationError: If feedback data is invalid
    """
    try:
        # Verify message exists and user has access
        message = get_message_by_id(db, str(feedback_data.message_id), current_user.id)
        if not message:
            raise NotFoundError("Message not found or access denied")
        
        # Check if feedback already exists for this message by this user
        existing_feedback = db.query(Feedback).filter(
            and_(
                Feedback.message_id == message.id,
                Feedback.user_id == current_user.id
            )
        ).first()
        
        if existing_feedback:
            raise ConflictError("Feedback already exists for this message")
        
        # Create new feedback
        feedback = Feedback(
            user_id=current_user.id,
            session_id=message.session_id,
            message_id=message.id,
            feedback_type=feedback_data.feedback_type,
            rating=feedback_data.rating,
            is_positive=feedback_data.is_positive,
            comment=feedback_data.comment,
            feedback_metadata=feedback_data.metadata or {}
        )
        
        db.add(feedback)
        db.commit()
        db.refresh(feedback)
        
        logger.info(f"Created feedback {feedback.id} for message {message.id} by user {current_user.id}")
        
        return FeedbackResponse.from_orm(feedback)
        
    except (NotFoundError, ConflictError, ValidationError):
        raise
    except Exception as e:
        logger.error(f"Failed to create feedback: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create feedback"
        )


@router.get("/", response_model=FeedbackListResponse)
async def list_feedback(
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of items to return"),
    feedback_type: Optional[str] = Query(None, description="Filter by feedback type"),
    session_id: Optional[str] = Query(None, description="Filter by session ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    List feedback for the current user.
    
    Args:
        skip: Number of items to skip (for pagination)
        limit: Number of items to return
        feedback_type: Optional filter by feedback type
        session_id: Optional filter by session ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        FeedbackListResponse: List of feedback with pagination info
    """
    try:
        query = db.query(Feedback).filter(Feedback.user_id == current_user.id)
        
        # Apply filters
        if feedback_type:
            query = query.filter(Feedback.feedback_type == feedback_type)
        
        if session_id:
            try:
                session_uuid = uuid.UUID(session_id)
                query = query.filter(Feedback.session_id == session_uuid)
            except ValueError:
                raise ValidationError("Invalid session ID format")
        
        # Get total count
        total = query.count()
        
        # Apply pagination and ordering
        feedback_list = query.order_by(Feedback.created_at.desc()).offset(skip).limit(limit).all()
        
        return FeedbackListResponse(
            feedback=[FeedbackResponse.from_orm(f) for f in feedback_list],
            total=total,
            page=skip // limit + 1,
            page_size=limit
        )
        
    except ValidationError:
        raise
    except Exception as e:
        logger.error(f"Failed to list feedback: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve feedback"
        )


@router.get("/with-context", response_model=List[FeedbackWithContext])
async def list_feedback_with_context(
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of items to return (max 100 for context queries)"),
    feedback_type: Optional[str] = Query(None, description="Filter by feedback type"),
    session_id: Optional[str] = Query(None, description="Filter by session ID"),
    include_conversation: bool = Query(False, description="Include recent conversation context"),
    context_size: int = Query(3, ge=1, le=10, description="Number of recent messages to include"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    List feedback with message context for analysis and understanding.

    Args:
        skip: Number of items to skip (for pagination)
        limit: Number of items to return (limited to 100 for performance)
        feedback_type: Optional filter by feedback type
        session_id: Optional filter by session ID
        include_conversation: Whether to include conversation context
        context_size: Number of recent messages to include in context
        current_user: Current authenticated user
        db: Database session

    Returns:
        List[FeedbackWithContext]: List of feedback with context
    """
    try:
        query = db.query(Feedback).filter(Feedback.user_id == current_user.id)

        # Apply filters
        if feedback_type:
            query = query.filter(Feedback.feedback_type == feedback_type)

        if session_id:
            try:
                session_uuid = uuid.UUID(session_id)
                query = query.filter(Feedback.session_id == session_uuid)
            except ValueError:
                raise ValidationError("Invalid session ID format")

        # Apply pagination and ordering
        feedback_list = query.order_by(Feedback.created_at.desc()).offset(skip).limit(limit).all()

        # Build response with context
        result = []
        for feedback in feedback_list:
            # Get the message being rated
            agent_message = db.query(Message).filter(Message.id == feedback.message_id).first()
            if not agent_message:
                continue  # Skip if message not found

            # Get message context
            user_question, conversation_context = get_message_context(db, agent_message, context_size)

            # Create response
            response_data = FeedbackResponse.from_orm(feedback).model_dump()
            response_data["agent_message"] = create_message_context(agent_message)

            if user_question:
                response_data["user_question"] = create_message_context(user_question)

            if include_conversation and conversation_context:
                response_data["conversation_context"] = [
                    create_message_context(msg) for msg in conversation_context
                ]

            result.append(FeedbackWithContext(**response_data))

        return result

    except ValidationError:
        raise
    except Exception as e:
        logger.error(f"Failed to list feedback with context: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve feedback with context"
        )


@router.get("/stats", response_model=FeedbackStatsResponse)
async def get_feedback_stats(
    session_id: Optional[str] = Query(None, description="Filter stats by session ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get feedback statistics for the current user.
    
    Args:
        session_id: Optional filter by session ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        FeedbackStatsResponse: Feedback statistics
    """
    try:
        query = db.query(Feedback).filter(Feedback.user_id == current_user.id)
        
        if session_id:
            try:
                session_uuid = uuid.UUID(session_id)
                query = query.filter(Feedback.session_id == session_uuid)
            except ValueError:
                raise ValidationError("Invalid session ID format")
        
        # Get basic counts
        total_feedback = query.count()
        thumbs_up = query.filter(Feedback.feedback_type == 'thumbs_up').count()
        thumbs_down = query.filter(Feedback.feedback_type == 'thumbs_down').count()
        total_comments = query.filter(Feedback.feedback_type == 'comment').count()
        
        # Get rating statistics
        rating_query = query.filter(Feedback.feedback_type == 'rating')
        total_ratings = rating_query.count()
        
        average_rating = None
        if total_ratings > 0:
            avg_result = rating_query.with_entities(func.avg(Feedback.rating)).scalar()
            average_rating = round(float(avg_result), 2) if avg_result else None
        
        # Calculate positive percentage
        positive_percentage = None
        total_boolean_feedback = thumbs_up + thumbs_down
        if total_boolean_feedback > 0:
            positive_percentage = round((thumbs_up / total_boolean_feedback) * 100, 2)
        
        return FeedbackStatsResponse(
            total_feedback=total_feedback,
            thumbs_up=thumbs_up,
            thumbs_down=thumbs_down,
            average_rating=average_rating,
            total_ratings=total_ratings,
            total_comments=total_comments,
            positive_percentage=positive_percentage
        )

    except ValidationError:
        raise
    except Exception as e:
        logger.error(f"Failed to get feedback stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve feedback statistics"
        )


@router.get("/{feedback_id}", response_model=FeedbackResponse)
async def get_feedback(
    feedback_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific feedback by ID.

    Args:
        feedback_id: Feedback ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        FeedbackResponse: Feedback details

    Raises:
        NotFoundError: If feedback not found or user doesn't have access
    """
    try:
        feedback = get_feedback_by_id(db, feedback_id, current_user.id)
        if not feedback:
            raise NotFoundError("Feedback not found")

        return FeedbackResponse.from_orm(feedback)

    except NotFoundError:
        raise
    except Exception as e:
        logger.error(f"Failed to get feedback {feedback_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve feedback"
        )


@router.get("/{feedback_id}/context", response_model=FeedbackWithContext)
async def get_feedback_with_context(
    feedback_id: str,
    include_conversation: bool = Query(False, description="Include recent conversation context"),
    context_size: int = Query(5, ge=1, le=20, description="Number of recent messages to include"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get feedback with message context (user question and agent response).

    Args:
        feedback_id: Feedback ID
        include_conversation: Whether to include recent conversation context
        context_size: Number of recent messages to include in conversation context
        current_user: Current authenticated user
        db: Database session

    Returns:
        FeedbackWithContext: Feedback with message context

    Raises:
        NotFoundError: If feedback not found or user doesn't have access
    """
    try:
        feedback = get_feedback_by_id(db, feedback_id, current_user.id)
        if not feedback:
            raise NotFoundError("Feedback not found")

        # Get the message being rated
        agent_message = db.query(Message).filter(Message.id == feedback.message_id).first()
        if not agent_message:
            raise NotFoundError("Associated message not found")

        # Get message context
        user_question, conversation_context = get_message_context(db, agent_message, context_size)

        # Create response
        response_data = FeedbackResponse.from_orm(feedback).model_dump()
        response_data["agent_message"] = create_message_context(agent_message)

        if user_question:
            response_data["user_question"] = create_message_context(user_question)

        if include_conversation and conversation_context:
            response_data["conversation_context"] = [
                create_message_context(msg) for msg in conversation_context
            ]

        return FeedbackWithContext(**response_data)

    except NotFoundError:
        raise
    except Exception as e:
        logger.error(f"Failed to get feedback context {feedback_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve feedback context"
        )


@router.put("/{feedback_id}", response_model=FeedbackResponse)
async def update_feedback(
    feedback_id: str,
    feedback_data: FeedbackUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update feedback.

    Args:
        feedback_id: Feedback ID
        feedback_data: Feedback update data
        current_user: Current authenticated user
        db: Database session

    Returns:
        FeedbackResponse: Updated feedback

    Raises:
        NotFoundError: If feedback not found or user doesn't have access
    """
    try:
        feedback = get_feedback_by_id(db, feedback_id, current_user.id)
        if not feedback:
            raise NotFoundError("Feedback not found")

        # Update fields if provided
        if feedback_data.rating is not None:
            feedback.rating = feedback_data.rating
        if feedback_data.is_positive is not None:
            feedback.is_positive = feedback_data.is_positive
        if feedback_data.comment is not None:
            feedback.comment = feedback_data.comment
        if feedback_data.metadata is not None:
            feedback.feedback_metadata = feedback_data.metadata

        db.commit()
        db.refresh(feedback)

        logger.info(f"Updated feedback {feedback.id} by user {current_user.id}")

        return FeedbackResponse.from_orm(feedback)

    except NotFoundError:
        raise
    except Exception as e:
        logger.error(f"Failed to update feedback {feedback_id}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update feedback"
        )


@router.delete("/{feedback_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_feedback(
    feedback_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete feedback.

    Args:
        feedback_id: Feedback ID
        current_user: Current authenticated user
        db: Database session

    Raises:
        NotFoundError: If feedback not found or user doesn't have access
    """
    try:
        feedback = get_feedback_by_id(db, feedback_id, current_user.id)
        if not feedback:
            raise NotFoundError("Feedback not found")

        db.delete(feedback)
        db.commit()

        logger.info(f"Deleted feedback {feedback.id} by user {current_user.id}")

    except NotFoundError:
        raise
    except Exception as e:
        logger.error(f"Failed to delete feedback {feedback_id}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete feedback"
        )
