"""Add image_data to roles table

Revision ID: 001_add_image_data
Revises: None
Create Date: 2025-01-18 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '001_add_image_data'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add image_data column to roles table for base64 encoded images
    op.add_column('roles', sa.Column('image_data', sa.Text(), nullable=True))


def downgrade() -> None:
    # Remove image_data column
    op.drop_column('roles', 'image_data')