"""
Pinecone search tools for AI agents.
"""

import logging
from typing import Dict, Any, List, Optional, Union

from app.agent.tools.base import BaseTool, ToolSchema, ToolResult, tool_registry
from app.core.config import settings
from app.embedding.service import EmbeddingService
from pinecone import Pinecone

logger = logging.getLogger(__name__)


class PineconeSearchTool(BaseTool):
    """Tool for searching a Pinecone index."""

    def __init__(self):
        super().__init__()
        self._pc = None
        self._index = None
        self._embedding_service = None
        self.index_name = settings.pinecone_index_name

    def _initialize_pinecone(self):
        """Lazy initialization of Pinecone connection."""
        if self._pc is None:
            if not settings.pinecone_api_key:
                raise ValueError("Pinecone API key not configured")
            if not settings.pinecone_index_name:
                raise ValueError("Pinecone index name not configured")

            self._pc = Pinecone(api_key=settings.pinecone_api_key)
            self._index = self._pc.Index(self.index_name)
            self._embedding_service = EmbeddingService()

    @property
    def index(self):
        """Get the Pinecone index with lazy initialization."""
        self._initialize_pinecone()
        return self._index

    @property
    def embedding_service(self):
        """Get the embedding service with lazy initialization."""
        self._initialize_pinecone()
        return self._embedding_service

    @property
    def name(self) -> str:
        return "pinecone_search"

    @property
    def description(self) -> str:
        return "Search for data in a Pinecone index using text query(s)"

    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "query_text": {
                        "oneOf": [
                            {
                                "type": "string",
                                "description": "A single text to search for"
                            },
                            {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Multiple texts to search for"
                            }
                        ],
                        "description": "The text(s) to search for - can be a single string or array of strings",
                    },
                    "top_k": {
                        "type": "integer",
                        "description": "The number of results to return",
                        "default": 5,
                    },
                    "namespace": {
                        "type": "string",
                        "description": "The namespace to search in",
                        "default": None,
                    }
                },
                "required": ["query_text"],
            },
        )

    async def execute(self, user_context: Optional[Dict[str, Any]] = None, **kwargs) -> ToolResult:
        """
        Perform a search in the Pinecone index.

        Args:
            user_context: User context containing user_id, tenant_id, client_id, session_key
            query_text: The text(s) to search for - can be a single string or array of strings.
            top_k: The number of results to return.
            namespace: The namespace to search in.

        Returns:
            ToolResult with search results.
        """
        try:
            # Initialize Pinecone connection if needed
            try:
                self._initialize_pinecone()
            except ValueError as ve:
                return ToolResult(success=False, error=f"Pinecone configuration error: {str(ve)}")

            query_text = kwargs.get("query_text")
            top_k = kwargs.get("top_k", 5)
            namespace = kwargs.get("namespace")
            client_id = user_context.get("client_id") if user_context else None

            if not query_text:
                return ToolResult(success=False, error="Query text is required")

            # Handle both single string and array of strings
            if isinstance(query_text, str):
                # Single query text
                query_vector = await self.embedding_service.embed_text(query_text)
                return await self._perform_single_search(query_vector, top_k, namespace, client_id)
            elif isinstance(query_text, list):
                # Multiple query texts
                if not query_text:
                    return ToolResult(success=False, error="Query text array cannot be empty")

                # Get embeddings for all query texts
                query_vectors = await self.embedding_service.embed_texts(query_text)
                return await self._perform_multiple_searches(query_text, query_vectors, top_k, namespace, client_id)
            else:
                return ToolResult(success=False, error="Query text must be a string or array of strings")

        except Exception as e:
            logger.error(f"Pinecone search error: {str(e)}")
            return ToolResult(success=False, error=f"Pinecone search failed: {str(e)}")

    async def _perform_single_search(
        self,
        query_vector: List[float],
        top_k: int,
        namespace: Optional[str],
        client_id: Optional[str]
    ) -> ToolResult:
        """Perform a single search with one query vector."""
        try:
            # Build the query arguments
            query_args = {
                "vector": query_vector,
                "top_k": top_k,
                "include_metadata": True,
                "namespace": namespace,
            }

            # Add metadata filtering if client_id is provided
            if client_id:
                query_args["filter"] = {"xperience_clients": {"$in": [client_id]}}

            results = self.index.query(**query_args)

            return ToolResult(
                success=True,
                result=results.to_dict(),
            )
        except Exception as e:
            logger.error(f"Single Pinecone search error: {str(e)}")
            return ToolResult(success=False, error=f"Single Pinecone search failed: {str(e)}")

    async def _perform_multiple_searches(
        self,
        query_texts: List[str],
        query_vectors: List[List[float]],
        top_k: int,
        namespace: Optional[str],
        client_id: Optional[str]
    ) -> ToolResult:
        """Perform multiple searches with multiple query vectors."""
        try:
            all_results = []

            for i, (query_text, query_vector) in enumerate(zip(query_texts, query_vectors)):
                # Build the query arguments for each search
                query_args = {
                    "vector": query_vector,
                    "top_k": top_k,
                    "include_metadata": True,
                    "namespace": namespace,
                }

                # Add metadata filtering if client_id is provided
                if client_id:
                    query_args["filter"] = {"xperience_clients": {"$in": [client_id]}}

                results = self.index.query(**query_args)

                # Add query information to results
                result_dict = results.to_dict()
                result_dict["query_index"] = i
                result_dict["query_text"] = query_text
                all_results.append(result_dict)

            return ToolResult(
                success=True,
                result={
                    "searches": all_results,
                    "total_queries": len(query_texts)
                },
            )
        except Exception as e:
            logger.error(f"Multiple Pinecone search error: {str(e)}")
            return ToolResult(success=False, error=f"Multiple Pinecone search failed: {str(e)}")


# Register tools with error handling
try:
    tool_registry.register_tool(PineconeSearchTool())
    logger.info("Pinecone search tool registered successfully")
except Exception as e:
    logger.warning(f"Failed to register Pinecone search tool: {str(e)}. Tool will be unavailable.")
