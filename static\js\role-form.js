// Role form JavaScript

let availableTools = [];
let selectedTools = new Set();
let toolCategories = new Set();
let originalImageUrl = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    initializeForm();
    loadTools();
    setupEventListeners();
    initializeTooltips();
});

// Initialize form with existing role data
function initializeForm() {
    if (window.roleData) {
        const role = window.roleData;
        
        // Set selected tools
        if (role.tools) {
            selectedTools = new Set(role.tools);
        }
        
        // Set form values
        document.getElementById('name').value = role.name || '';
        document.getElementById('display_name').value = role.display_name || '';
        document.getElementById('description').value = role.description || '';
        document.getElementById('system_prompt').value = role.system_prompt || '';
        document.getElementById('temperature').value = role.config?.temperature || '0.7';
        document.getElementById('max_tokens').value = role.config?.max_tokens || '1000';
        document.getElementById('is_active').checked = role.is_active !== false;

        // Store original image URL
        if (role.image_url) {
            originalImageUrl = role.image_url;
            document.getElementById('image_url').value = originalImageUrl;
        }
    }
}

// Load available tools
async function loadTools() {
    try {
        utils.showLoading(true, 'Loading tools...');

        const response = await utils.apiGet('/tools/');
        availableTools = response.tools;

        // Extract categories
        toolCategories = new Set(availableTools.map(tool => tool.category));

        renderToolCategories();
        renderTools();

    } catch (error) {
        console.error('Failed to load tools:', error);
        utils.showAlert('Failed to load tools: ' + error.message, 'warning');
    } finally {
        utils.showLoading(false);
    }
}

// Render tool categories
function renderToolCategories() {
    const container = document.getElementById('toolCategories');
    
    const categoriesHtml = Array.from(toolCategories).map(category => `
        <div class="form-check mb-1">
            <input class="form-check-input" type="checkbox" id="category-${category}" 
                   onchange="toggleCategory('${category}')">
            <label class="form-check-label" for="category-${category}">
                <small><strong>${category.charAt(0).toUpperCase() + category.slice(1)}</strong></small>
            </label>
        </div>
    `).join('');
    
    container.innerHTML = categoriesHtml;
}

// Render tools list
function renderTools() {
    const container = document.getElementById('toolsList');
    const searchTerm = document.getElementById('toolSearch')?.value.toLowerCase() || '';
    
    let filteredTools = availableTools;
    
    // Filter by search term
    if (searchTerm) {
        filteredTools = availableTools.filter(tool => 
            tool.name.toLowerCase().includes(searchTerm) ||
            tool.description.toLowerCase().includes(searchTerm) ||
            tool.category.toLowerCase().includes(searchTerm)
        );
    }
    
    // Group tools by category
    const toolsByCategory = {};
    filteredTools.forEach(tool => {
        if (!toolsByCategory[tool.category]) {
            toolsByCategory[tool.category] = [];
        }
        toolsByCategory[tool.category].push(tool);
    });
    
    const toolsHtml = Object.entries(toolsByCategory).map(([category, tools]) => `
        <div class="mb-3">
            <div class="tool-category">
                <i class="fas fa-folder me-1"></i>
                ${category.charAt(0).toUpperCase() + category.slice(1)}
            </div>
            ${tools.map(tool => `
                <div class="tool-item ${selectedTools.has(tool.name) ? 'selected' : ''}" 
                     onclick="toggleTool('${tool.name}')">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" 
                               id="tool-${tool.name}" ${selectedTools.has(tool.name) ? 'checked' : ''}>
                        <label class="form-check-label w-100" for="tool-${tool.name}">
                            <div class="tool-name">${utils.escapeHtml(tool.name)}</div>
                            <div class="tool-description">${utils.escapeHtml(tool.description)}</div>
                            <div class="tool-parameters">
                                <i class="fas fa-cog me-1"></i>
                                Required: ${tool.required.length > 0 ? tool.required.join(', ') : 'None'}
                            </div>
                        </label>
                    </div>
                </div>
            `).join('')}
        </div>
    `).join('');
    
    container.innerHTML = toolsHtml || '<p class="text-muted">No tools found</p>';
    
    updateSelectAllState();
}

// Toggle tool selection
function toggleTool(toolName) {
    if (selectedTools.has(toolName)) {
        selectedTools.delete(toolName);
    } else {
        selectedTools.add(toolName);
    }
    
    // Update checkbox
    const checkbox = document.getElementById(`tool-${toolName}`);
    if (checkbox) {
        checkbox.checked = selectedTools.has(toolName);
    }
    
    // Update visual state
    const toolItem = checkbox?.closest('.tool-item');
    if (toolItem) {
        toolItem.classList.toggle('selected', selectedTools.has(toolName));
    }
    
    updateSelectAllState();
}

// Toggle category selection
function toggleCategory(category) {
    const categoryCheckbox = document.getElementById(`category-${category}`);
    const isChecked = categoryCheckbox.checked;
    
    const categoryTools = availableTools.filter(tool => tool.category === category);
    
    categoryTools.forEach(tool => {
        if (isChecked) {
            selectedTools.add(tool.name);
        } else {
            selectedTools.delete(tool.name);
        }
    });
    
    renderTools();
}

// Toggle all tools
function toggleAllTools() {
    const selectAllCheckbox = document.getElementById('selectAllTools');
    const isChecked = selectAllCheckbox.checked;
    
    if (isChecked) {
        availableTools.forEach(tool => selectedTools.add(tool.name));
    } else {
        selectedTools.clear();
    }
    
    renderTools();
}

// Update select all checkbox state
function updateSelectAllState() {
    const selectAllCheckbox = document.getElementById('selectAllTools');
    if (selectAllCheckbox) {
        const totalTools = availableTools.length;
        const selectedCount = selectedTools.size;
        
        selectAllCheckbox.checked = selectedCount === totalTools;
        selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < totalTools;
    }
}

// Filter tools
function filterTools() {
    renderTools();
}

// Refresh tools
async function refreshTools() {
    await loadTools();
    utils.showAlert('Tools refreshed', 'success', 2000);
}

// Initialize Bootstrap tooltips
function initializeTooltips() {
    // Initialize all tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Setup event listeners
function setupEventListeners() {
    const form = document.getElementById('roleForm');
    form.addEventListener('submit', handleFormSubmit);
    
    // Auto-format role name
    const nameInput = document.getElementById('name');
    if (nameInput && !nameInput.readOnly) {
        nameInput.addEventListener('input', function() {
            this.value = this.value.toLowerCase().replace(/[^a-z0-9_-]/g, '');
        });
    }
}

// Handle form submission
async function handleFormSubmit(event) {
    event.preventDefault();
    
    const form = event.target;
    
    // Validate form
    if (!utils.validateForm(form)) {
        utils.showAlert('Please fill in all required fields', 'warning');
        return;
    }
    
    // Validate tools selection
    if (selectedTools.size === 0) {
        const confirmed = confirm('No tools selected. The role will have no special capabilities. Continue?');
        if (!confirmed) return;
    }
    
    try {
        utils.showLoading(true, 'Saving role...');
        
        const isUpdate = !!window.roleData;

        // Prepare form data
        const imageData = document.getElementById('image_url').value;
        const formData = {
            display_name: document.getElementById('display_name').value.trim(),
            description: document.getElementById('description').value.trim(),
            system_prompt: document.getElementById('system_prompt').value.trim(),
            tools: Array.from(selectedTools),
            config: {
                temperature: parseFloat(document.getElementById('temperature').value),
                max_tokens: parseInt(document.getElementById('max_tokens').value)
            },
            image_data: imageData.startsWith('data:image') ? imageData : null,
            image_url: !imageData.startsWith('data:image') ? imageData : null,
            is_active: document.getElementById('is_active').checked
        };

        if (!isUpdate) {
            formData.name = document.getElementById('name').value.trim();
        }

        // Submit form
        let response;
        if (isUpdate) {
            // Update existing role
            response = await utils.apiPut(`/roles/${window.roleData.name}`, formData);
        } else {
            // Create new role
            response = await utils.apiPost('/roles', formData);
        }
        
        utils.showAlert(
            `Role "${formData.display_name}" ${window.roleData ? 'updated' : 'created'} successfully`,
            'success'
        );
        
        // Redirect to roles list after a short delay
        setTimeout(() => {
            window.location.href = '/roles';
        }, 1500);
        
    } catch (error) {
        console.error('Failed to save role:', error);
        utils.showAlert('Failed to save role: ' + error.message, 'danger');
    } finally {
        utils.showLoading(false);
    }
}

// Test role function
function testRole() {
    if (window.roleData) {
        window.location.href = `/test?role=${encodeURIComponent(window.roleData.name)}`;
    }
}

// Image handling functions
function previewImage(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        utils.showAlert('Please select a valid image file (PNG, JPG, GIF, or WebP)', 'warning');
        event.target.value = '';
        return;
    }
    
    // Validate file size (5MB max)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
        utils.showAlert('Image file must be smaller than 5MB', 'warning');
        event.target.value = '';
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        const base64Data = e.target.result;
        
        // Update preview
        const previewContainer = document.getElementById('imagePreview');
        previewContainer.innerHTML = `
            <img src="${base64Data}" alt="Role Image" class="img-fluid rounded" style="max-width: 150px; max-height: 150px;">
        `;
        
        // Store base64 data
        document.getElementById('image_url').value = base64Data;
        
        // Show remove button if it doesn't exist
        const removeBtn = document.querySelector('.btn-outline-danger');
        if (!removeBtn) {
            const cardBody = document.querySelector('.card-body.d-flex.flex-column');
            const fileInputDiv = cardBody.querySelector('.mb-3:has(input[type="file"])');
            fileInputDiv.insertAdjacentHTML('afterend', `
                <button type="button" class="btn btn-sm btn-outline-danger mb-3" onclick="removeImage()">
                    <i class="fas fa-trash me-1"></i>
                    Remove Image
                </button>
            `);
        }
    };
    
    reader.readAsDataURL(file);
}

function removeImage() {
    // Clear the file input
    document.getElementById('role_image').value = '';
    
    // Clear the hidden image data
    document.getElementById('image_url').value = '';
    
    // Reset preview to default
    const previewContainer = document.getElementById('imagePreview');
    previewContainer.innerHTML = `
        <div class="border rounded d-flex align-items-center justify-content-center bg-light" style="width: 150px; height: 150px; margin: 0 auto;">
            <i class="fas fa-user-circle fa-4x text-muted"></i>
        </div>
    `;
    
    // Remove the remove button
    const removeBtn = document.querySelector('.btn-outline-danger');
    if (removeBtn) {
        removeBtn.remove();
    }
}
