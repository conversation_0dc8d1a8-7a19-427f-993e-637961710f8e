{% extends "base.html" %}

{% block title %}User Profile - Xapa AI{% endblock %}

{% block extra_head %}
<style>
    .profile-container {
        max-width: 800px;
        margin: 0 auto;
    }
    
    .profile-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .profile-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        border: 3px solid white;
        margin-bottom: 1rem;
    }
    
    .profile-name {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .profile-email {
        opacity: 0.9;
        font-size: 1rem;
    }
    
    .info-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
    }
    
    .info-card-header {
        background: #f8f9fa;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #dee2e6;
        border-radius: 10px 10px 0 0;
        font-weight: 600;
        color: #495057;
    }
    
    .info-card-body {
        padding: 1.5rem;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 500;
        color: #6c757d;
    }
    
    .info-value {
        color: #495057;
        font-weight: 500;
    }
    
    .badge-custom {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .auth0-info {
        background: #6705bd;
        color: white;
    }
    
    .local-info {
        background: #28a745;
        color: white;
    }
    
    .activity-item {
        padding: 1rem 0;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
    }
    
    .activity-login {
        background: #e3f2fd;
        color: #1976d2;
    }
    
    .activity-session {
        background: #f3e5f5;
        color: #7b1fa2;
    }
    
    .activity-role {
        background: #e8f5e8;
        color: #388e3c;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="profile-container">
        <!-- Profile Header -->
        <div class="profile-header">
            {% if user_info.get('picture') %}
            <img src="{{ user_info.picture }}" alt="Profile Picture" class="profile-avatar">
            {% else %}
            <div class="profile-avatar bg-light d-flex align-items-center justify-content-center">
                <i class="fas fa-user fa-2x text-muted"></i>
            </div>
            {% endif %}
            
            <div class="profile-name">
                {{ user_info.get('name', user.username) }}
            </div>
            <div class="profile-email">
                {{ user_info.get('email', user.email) }}
            </div>
            
            {% if user_info.get('sub', '').startswith('auth0|') %}
            <span class="badge badge-custom auth0-info mt-2">
                <i class="fas fa-shield-alt me-1"></i>
                Auth0 Account
            </span>
            {% else %}
            <span class="badge badge-custom local-info mt-2">
                <i class="fas fa-user me-1"></i>
                Local Account
            </span>
            {% endif %}
        </div>
        
        <!-- Account Information -->
        <div class="info-card">
            <div class="info-card-header">
                <i class="fas fa-user-circle me-2"></i>
                Account Information
            </div>
            <div class="info-card-body">
                <div class="info-row">
                    <span class="info-label">Username</span>
                    <span class="info-value">{{ user.username }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Email</span>
                    <span class="info-value">{{ user.email }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Account Status</span>
                    <span class="info-value">
                        {% if user.is_active %}
                        <span class="badge bg-success">Active</span>
                        {% else %}
                        <span class="badge bg-danger">Inactive</span>
                        {% endif %}
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">Member Since</span>
                    <span class="info-value">{{ user.created_at.strftime('%B %d, %Y') if user.created_at else 'Unknown' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Last Updated</span>
                    <span class="info-value">{{ user.updated_at.strftime('%B %d, %Y at %I:%M %p') if user.updated_at else 'Unknown' }}</span>
                </div>
            </div>
        </div>
        
        <!-- Auth0 Profile Information -->
        {% if user_info and user_info.get('sub', '').startswith('auth0|') %}
        <div class="info-card">
            <div class="info-card-header">
                <i class="fas fa-shield-alt me-2"></i>
                Auth0 Profile
            </div>
            <div class="info-card-body">
                {% if user_info.get('nickname') %}
                <div class="info-row">
                    <span class="info-label">Nickname</span>
                    <span class="info-value">{{ user_info.nickname }}</span>
                </div>
                {% endif %}
                
                {% if user_info.get('given_name') %}
                <div class="info-row">
                    <span class="info-label">First Name</span>
                    <span class="info-value">{{ user_info.given_name }}</span>
                </div>
                {% endif %}
                
                {% if user_info.get('family_name') %}
                <div class="info-row">
                    <span class="info-label">Last Name</span>
                    <span class="info-value">{{ user_info.family_name }}</span>
                </div>
                {% endif %}
                
                {% if user_info.get('locale') %}
                <div class="info-row">
                    <span class="info-label">Locale</span>
                    <span class="info-value">{{ user_info.locale }}</span>
                </div>
                {% endif %}
                
                {% if user_info.get('updated_at') %}
                <div class="info-row">
                    <span class="info-label">Profile Updated</span>
                    <span class="info-value">{{ user_info.updated_at }}</span>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        <!-- Quick Actions -->
        <div class="info-card">
            <div class="info-card-header">
                <i class="fas fa-bolt me-2"></i>
                Quick Actions
            </div>
            <div class="info-card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="/roles" class="btn btn-outline-primary w-100">
                            <i class="fas fa-users me-2"></i>
                            Manage Roles
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="/tools" class="btn btn-outline-success w-100">
                            <i class="fas fa-tools me-2"></i>
                            View Tools
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="/test" class="btn btn-outline-info w-100">
                            <i class="fas fa-flask me-2"></i>
                            Test Agent
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="/health" class="btn btn-outline-warning w-100">
                            <i class="fas fa-heart me-2"></i>
                            System Health
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Account Actions -->
        <div class="info-card">
            <div class="info-card-header">
                <i class="fas fa-cog me-2"></i>
                Account Actions
            </div>
            <div class="info-card-body text-center">
                <a href="/auth/logout" class="btn btn-outline-danger">
                    <i class="fas fa-sign-out-alt me-2"></i>
                    Logout
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Add any profile-specific JavaScript here
    console.log('Profile page loaded');
</script>
{% endblock %}
