from sqlalchemy import Column, String, Text, DateTime, <PERSON><PERSON><PERSON>, Integer, Foreign<PERSON>ey, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

from .base import Base


class Feedback(Base):
    """Feedback model for storing user feedback on AI responses."""
    
    __tablename__ = "feedback"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    session_id = Column(UUID(as_uuid=True), Foreign<PERSON>ey("sessions.id"), nullable=False)
    message_id = Column(UUID(as_uuid=True), ForeignKey("messages.id"), nullable=False)
    
    # Feedback type: 'thumbs_up', 'thumbs_down', 'rating', 'comment'
    feedback_type = Column(String(20), nullable=False, index=True)
    
    # Rating (1-5 scale) - optional, used when feedback_type is 'rating'
    rating = Column(Integer, nullable=True)
    
    # <PERSON><PERSON>an feedback - used for thumbs up/down
    is_positive = Column(Boolean, nullable=True)
    
    # Text feedback/comments
    comment = Column(Text, nullable=True)
    
    # Additional metadata (e.g., specific aspects rated, tags, etc.)
    feedback_metadata = Column(JSON, default={})
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", backref="feedback")
    session = relationship("Session", backref="feedback")
    message = relationship("Message", backref="feedback")
    
    def __repr__(self):
        return f"<Feedback(id={self.id}, type='{self.feedback_type}', user_id={self.user_id})>"
    
    def to_dict(self):
        """Convert feedback to dictionary."""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "session_id": str(self.session_id),
            "message_id": str(self.message_id),
            "feedback_type": self.feedback_type,
            "rating": self.rating,
            "is_positive": self.is_positive,
            "comment": self.comment,
            "metadata": self.feedback_metadata,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
