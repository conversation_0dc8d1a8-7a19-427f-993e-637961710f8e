from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from typing import List, Optional
import logging

from app.models.base import get_db
from app.models.user import User
from app.models.role import Role
from app.schemas.role import (
    RoleCreate,
    RoleUpdate,
    RoleResponse,
    RoleListResponse
)
from app.core.dependencies import get_current_user
from app.core.exceptions import NotFoundError, ConflictError, ValidationError
from app.agent.role_loader import role_loader
from app.agent.factory import agent_factory
from app.services.azure_storage import get_azure_storage_service
import base64

logger = logging.getLogger(__name__)

router = APIRouter()


import uuid
import mimetypes

def _upload_image_to_azure(image_data: str, role_name: str = None) -> Optional[str]:
    """Helper function to upload image to Azure Blob Storage."""
    if not image_data:
        return None
    
    try:
        storage_service = get_azure_storage_service()
        if not storage_service:
            logger.warning("Azure Storage not configured, keeping image_data as base64")
            return None
        
        # Extract base64 data and content type from data URL
        content_type = None
        if image_data.startswith('data:'):
            header, data = image_data.split(',', 1)
            content_type = header.split(';')[0].split(':')[1]
            image_bytes = base64.b64decode(data)
        else:
            # This case should ideally not happen if the frontend is sending data URLs
            image_bytes = base64.b64decode(image_data)

        # Determine file extension
        file_extension = mimetypes.guess_extension(content_type) if content_type else ''
        
        # Generate a unique blob name
        blob_name = f"roles/{role_name}_{uuid.uuid4().hex[:8]}{file_extension}"

        # Upload to Azure and get URL
        blob_url = storage_service.upload_image(image_bytes, blob_name, content_type=content_type)
        logger.info(f"Uploaded to Azure: {blob_url}")
        return blob_url
        
    except Exception as e:
        logger.error(f"Failed to upload image to Azure: {e}")
        return None


# Helper functions integrated from RoleService
def get_role_by_name(db: Session, name: str) -> Optional[Role]:
    """Get a role by name."""
    return db.query(Role).filter(Role.name == name).first()


def get_role_by_id(db: Session, role_id: str) -> Optional[Role]:
    """Get a role by ID."""
    return db.query(Role).filter(Role.id == role_id).first()


def list_roles_from_db(db: Session, active_only: bool = True) -> List[Role]:
    """List all roles, with an option to only return active roles."""
    query = db.query(Role)
    if active_only:
        query = query.filter(Role.is_active == True)
    return query.order_by(Role.is_active.desc(), Role.name).all()


def create_role_in_db(db: Session, role_data: RoleCreate) -> Role:
    """Create a new role."""
    # Check if role with same name already exists
    existing_role = get_role_by_name(db, role_data.name)
    if existing_role:
        raise ConflictError(f"Role with name '{role_data.name}' already exists")

    try:
        # Handle image upload to Azure Blob Storage
        image_url = None
        # Handle image upload to Azure if image_data is provided
        image_data = getattr(role_data, 'image_data', None)
        if image_data:
            image_url = _upload_image_to_azure(image_data, role_data.name)

        db_role = Role(
            name=role_data.name,
            display_name=role_data.display_name,
            description=role_data.description,
            system_prompt=role_data.system_prompt,
            tools=role_data.tools,
            config=role_data.config,
            image_url=image_url,
            is_active=role_data.is_active
        )
        db.add(db_role)
        db.commit()
        db.refresh(db_role)
        logger.info(f"Created role: {role_data.name}")
        return db_role
    except IntegrityError as e:
        db.rollback()
        raise ConflictError(f"Role creation failed: {str(e)}")


def update_role_in_db(db: Session, name: str, role_data: RoleUpdate) -> Optional[Role]:
    """Update an existing role."""
    role = get_role_by_name(db, name)
    if not role:
        raise NotFoundError(f"Role '{name}' not found")

    try:
        # Handle image upload to Azure if image_data is provided
        image_data = getattr(role_data, 'image_data', None)
        image_updated = False
        if image_data:
            image_url = _upload_image_to_azure(image_data, role.name)
            if image_url:
                role.image_url = image_url
                image_updated = True

        # Update only provided fields (excluding image_data as it's processed above)
        update_data = role_data.model_dump(exclude_unset=True, exclude={'image_data'})
        
        # If a new image was uploaded, do not update the image_url from the form
        if image_updated and 'image_url' in update_data:
            del update_data['image_url']

        for key, value in update_data.items():
            setattr(role, key, value)

        db.commit()
        db.refresh(role)
        logger.info(f"Updated role: {name}")
        return role
    except IntegrityError as e:
        db.rollback()
        raise ConflictError(f"Role update failed: {str(e)}")


def delete_role_in_db(db: Session, name: str, hard_delete: bool = False) -> bool:
    """Delete a role (soft delete by default, hard delete if specified)."""
    role = get_role_by_name(db, name)
    if not role:
        raise NotFoundError(f"Role '{name}' not found")

    if hard_delete:
        db.delete(role)
        db.commit()
        logger.info(f"Permanently deleted role: {name}")
    else:
        role.is_active = False
        db.commit()
        logger.info(f"Deactivated role: {name}")

    return True


@router.get("/", response_model=RoleListResponse)
async def list_roles(
    active_only: bool = Query(True, description="Filter only active roles"),
    db: Session = Depends(get_db)
):
    """
    List all roles from database.

    Args:
        db: Database session

    Returns:
        RoleListResponse: List of roles
    """
    roles = list_roles_from_db(db, active_only=active_only)

    role_responses = []
    for role in roles:
        role_responses.append(RoleResponse(
            id=str(role.id),
            name=role.name,
            display_name=role.display_name,
            description=role.description or "",
            system_prompt=role.system_prompt,
            tools=role.tools or [],
            config=role.config or {},
            image_url=role.image_url,
            is_active=role.is_active,
            created_at=role.created_at,
            updated_at=role.updated_at
        ))

    return RoleListResponse(roles=role_responses, total=len(role_responses))


@router.get("/{role_name}", response_model=RoleResponse)
async def get_role(
    role_name: str,
    db: Session = Depends(get_db)
):
    """
    Get a specific role by name from database.

    Args:
        role_name: Name of the role
        db: Database session

    Returns:
        RoleResponse: Role details

    Raises:
        HTTPException: If role not found
    """
    role = get_role_by_name(db, role_name)

    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Role '{role_name}' not found"
        )

    return RoleResponse(
        id=str(role.id),
        name=role.name,
        display_name=role.display_name,
        description=role.description or "",
        system_prompt=role.system_prompt,
        tools=role.tools or [],
        config=role.config or {},
        image_url=role.image_url,
        is_active=role.is_active,
        created_at=role.created_at,
        updated_at=role.updated_at
    )


@router.post("/", response_model=RoleResponse, status_code=status.HTTP_201_CREATED)
async def create_role(
    role_data: RoleCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new role in database (admin only).

    Args:
        role_data: Role creation data
        db: Database session
        current_user: Current authenticated user

    Returns:
        RoleResponse: Created role

    Raises:
        HTTPException: If role creation fails
    """
    try:
        role = create_role_in_db(db, role_data)

        # Reload roles in the role loader to include the new role
        role_loader.set_database_session(db)
        
        # Clear the agent factory's role cache to ensure new roles are picked up
        agent_factory.clear_role_cache()

        return RoleResponse(
            id=str(role.id),
            name=role.name,
            display_name=role.display_name,
            description=role.description or "",
            system_prompt=role.system_prompt,
            tools=role.tools or [],
            config=role.config or {},
            image_url=role.image_url,
            is_active=role.is_active,
            created_at=role.created_at,
            updated_at=role.updated_at
        )
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )


@router.put("/{role_name}", response_model=RoleResponse)
async def update_role(
    role_name: str,
    role_data: RoleUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update an existing role in database (admin only).

    Args:
        role_name: Name of the role to update
        role_data: Role update data
        db: Database session
        current_user: Current authenticated user

    Returns:
        RoleResponse: Updated role

    Raises:
        HTTPException: If role update fails
    """
    try:
        role = update_role_in_db(db, role_name, role_data)

        # Reload roles in the role loader to reflect changes
        role_loader.set_database_session(db)
        
        # Clear the agent factory's role cache to ensure updated roles are picked up
        agent_factory.clear_role_cache()

        return RoleResponse(
            id=str(role.id),
            name=role.name,
            display_name=role.display_name,
            description=role.description or "",
            system_prompt=role.system_prompt,
            tools=role.tools or [],
            config=role.config or {},
            image_url=role.image_url,
            is_active=role.is_active,
            created_at=role.created_at,
            updated_at=role.updated_at
        )
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )


@router.delete("/{role_name}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_role(
    role_name: str,
    hard_delete: bool = Query(False, description="Permanently delete role instead of soft delete"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a role from database (admin only).

    Args:
        role_name: Name of the role to delete
        hard_delete: Whether to permanently delete the role
        db: Database session
        current_user: Current authenticated user

    Raises:
        HTTPException: If role deletion fails
    """
    try:
        delete_role_in_db(db, role_name, hard_delete=hard_delete)

        # Reload roles in the role loader to reflect changes
        role_loader.set_database_session(db)
        
        # Clear the agent factory's role cache to ensure deleted roles are removed
        agent_factory.clear_role_cache()

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
