"""
Writing assistance tools for AI agents.

This module contains tools for grammar checking, style analysis, and writing assistance.
"""

import logging
import re
from typing import Dict, Any, List, Optional

from app.agent.tools.base import BaseTool, ToolSchema, ToolResult, tool_registry

logger = logging.getLogger(__name__)


class GrammarCheckerTool(BaseTool):
    """Tool for basic grammar checking and text analysis."""
    
    @property
    def name(self) -> str:
        return "grammar_checker"
    
    @property
    def description(self) -> str:
        return "Check grammar, spelling, and basic writing issues in text"
    
    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "Text to check for grammar and spelling issues"
                    },
                    "check_type": {
                        "type": "string",
                        "description": "Type of check to perform",
                        "enum": ["basic", "detailed", "spelling_only"],
                        "default": "basic"
                    }
                },
                "required": ["text"]
            }
        )
    
    async def execute(self, user_context: Optional[Dict[str, Any]] = None, **kwargs) -> ToolResult:
        """
        Check text for grammar and spelling issues.
        
        Args:
            user_context: User context containing user_id, tenant_id, client_id, session_key
            text: Text to check
            check_type: Type of check to perform
            
        Returns:
            ToolResult with grammar check results
        """
        try:
            text = kwargs.get("text", "").strip()
            check_type = kwargs.get("check_type", "basic")
            
            if not text:
                return ToolResult(
                    success=False,
                    error="No text provided"
                )
            
            issues = []
            suggestions = []
            
            # Basic grammar and style checks
            if check_type in ["basic", "detailed"]:
                issues.extend(self._check_basic_grammar(text))
                issues.extend(self._check_punctuation(text))
                suggestions.extend(self._get_style_suggestions(text))
            
            if check_type in ["spelling_only", "basic", "detailed"]:
                issues.extend(self._check_spelling(text))
            
            if check_type == "detailed":
                suggestions.extend(self._get_advanced_suggestions(text))
            
            # Calculate readability score
            readability = self._calculate_readability(text)
            
            return ToolResult(
                success=True,
                result={
                    "text": text,
                    "issues": issues,
                    "suggestions": suggestions,
                    "readability": readability,
                    "word_count": len(text.split()),
                    "character_count": len(text),
                    "sentence_count": len(re.split(r'[.!?]+', text)) - 1
                },
                metadata={
                    "operation": "grammar_check",
                    "check_type": check_type,
                    "issues_found": len(issues)
                }
            )
            
        except Exception as e:
            logger.error(f"Grammar check error: {str(e)}")
            return ToolResult(
                success=False,
                error=f"Grammar check failed: {str(e)}"
            )
    
    def _check_basic_grammar(self, text: str) -> List[Dict[str, Any]]:
        """Check for basic grammar issues."""
        issues = []
        
        # Check for double spaces
        if "  " in text:
            issues.append({
                "type": "spacing",
                "message": "Multiple consecutive spaces found",
                "severity": "minor"
            })
        
        # Check for missing capitalization at sentence start
        sentences = re.split(r'[.!?]+', text)
        for i, sentence in enumerate(sentences[:-1]):  # Exclude last empty element
            sentence = sentence.strip()
            if sentence and not sentence[0].isupper():
                issues.append({
                    "type": "capitalization",
                    "message": f"Sentence should start with capital letter: '{sentence[:20]}...'",
                    "severity": "medium"
                })
        
        return issues
    
    def _check_punctuation(self, text: str) -> List[Dict[str, Any]]:
        """Check for punctuation issues."""
        issues = []
        
        # Check for space before punctuation
        if re.search(r'\s+[,.!?;:]', text):
            issues.append({
                "type": "punctuation",
                "message": "Space found before punctuation mark",
                "severity": "minor"
            })
        
        # Check for missing space after punctuation
        if re.search(r'[,.!?;:][a-zA-Z]', text):
            issues.append({
                "type": "punctuation",
                "message": "Missing space after punctuation mark",
                "severity": "minor"
            })
        
        return issues
    
    def _check_spelling(self, text: str) -> List[Dict[str, Any]]:
        """Basic spelling check (simplified)."""
        issues = []
        
        # Common misspellings
        common_errors = {
            "teh": "the",
            "recieve": "receive",
            "seperate": "separate",
            "definately": "definitely",
            "occured": "occurred",
            "accomodate": "accommodate"
        }
        
        words = re.findall(r'\b\w+\b', text.lower())
        for word in words:
            if word in common_errors:
                issues.append({
                    "type": "spelling",
                    "message": f"Possible misspelling: '{word}' -> '{common_errors[word]}'",
                    "severity": "medium",
                    "suggestion": common_errors[word]
                })
        
        return issues
    
    def _get_style_suggestions(self, text: str) -> List[Dict[str, Any]]:
        """Get basic style suggestions."""
        suggestions = []
        
        # Check for passive voice (simplified)
        passive_indicators = ["was", "were", "been", "being"]
        for indicator in passive_indicators:
            if f" {indicator} " in text.lower():
                suggestions.append({
                    "type": "style",
                    "message": "Consider using active voice instead of passive voice",
                    "severity": "suggestion"
                })
                break
        
        # Check sentence length
        sentences = re.split(r'[.!?]+', text)
        long_sentences = [s for s in sentences if len(s.split()) > 25]
        if long_sentences:
            suggestions.append({
                "type": "style",
                "message": f"Consider breaking up long sentences ({len(long_sentences)} found)",
                "severity": "suggestion"
            })
        
        return suggestions
    
    def _get_advanced_suggestions(self, text: str) -> List[Dict[str, Any]]:
        """Get advanced writing suggestions."""
        suggestions = []
        
        # Check for repetitive words
        words = re.findall(r'\b\w+\b', text.lower())
        word_count = {}
        for word in words:
            if len(word) > 3:  # Only check longer words
                word_count[word] = word_count.get(word, 0) + 1
        
        repetitive_words = [word for word, count in word_count.items() if count > 3]
        if repetitive_words:
            suggestions.append({
                "type": "style",
                "message": f"Consider varying word choice. Repetitive words: {', '.join(repetitive_words[:3])}",
                "severity": "suggestion"
            })
        
        return suggestions
    
    def _calculate_readability(self, text: str) -> Dict[str, Any]:
        """Calculate basic readability metrics."""
        sentences = len(re.split(r'[.!?]+', text)) - 1
        words = len(text.split())
        characters = len(text.replace(' ', ''))
        
        if sentences == 0 or words == 0:
            return {"score": 0, "level": "unknown"}
        
        # Simplified readability score
        avg_sentence_length = words / sentences
        avg_word_length = characters / words
        
        # Simple scoring (not a real readability formula)
        score = max(0, 100 - (avg_sentence_length * 2) - (avg_word_length * 5))
        
        if score >= 80:
            level = "very easy"
        elif score >= 60:
            level = "easy"
        elif score >= 40:
            level = "moderate"
        elif score >= 20:
            level = "difficult"
        else:
            level = "very difficult"
        
        return {
            "score": round(score, 1),
            "level": level,
            "avg_sentence_length": round(avg_sentence_length, 1),
            "avg_word_length": round(avg_word_length, 1)
        }


class StyleAnalyzerTool(BaseTool):
    """Tool for analyzing writing style and tone."""
    
    @property
    def name(self) -> str:
        return "style_analyzer"
    
    @property
    def description(self) -> str:
        return "Analyze writing style, tone, and provide improvement suggestions"
    
    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "Text to analyze for style and tone"
                    },
                    "target_style": {
                        "type": "string",
                        "description": "Target writing style",
                        "enum": ["formal", "informal", "academic", "creative", "business"],
                        "default": "formal"
                    }
                },
                "required": ["text"]
            }
        )
    
    async def execute(self, user_context: Optional[Dict[str, Any]] = None, **kwargs) -> ToolResult:
        """
        Analyze text style and tone.
        
        Args:
            user_context: User context containing user_id, tenant_id, client_id, session_key
            text: Text to analyze
            target_style: Target writing style
            
        Returns:
            ToolResult with style analysis
        """
        try:
            text = kwargs.get("text", "").strip()
            target_style = kwargs.get("target_style", "formal")
            
            if not text:
                return ToolResult(
                    success=False,
                    error="No text provided"
                )
            
            # Analyze current style
            current_style = self._analyze_current_style(text)
            tone_analysis = self._analyze_tone(text)
            style_suggestions = self._get_style_suggestions(text, target_style)
            
            return ToolResult(
                success=True,
                result={
                    "text": text,
                    "current_style": current_style,
                    "target_style": target_style,
                    "tone_analysis": tone_analysis,
                    "style_suggestions": style_suggestions,
                    "word_count": len(text.split()),
                    "complexity_score": self._calculate_complexity(text)
                },
                metadata={
                    "operation": "style_analysis",
                    "target_style": target_style
                }
            )
            
        except Exception as e:
            logger.error(f"Style analysis error: {str(e)}")
            return ToolResult(
                success=False,
                error=f"Style analysis failed: {str(e)}"
            )
    
    def _analyze_current_style(self, text: str) -> Dict[str, Any]:
        """Analyze the current writing style."""
        # Simple style indicators
        formal_indicators = ["therefore", "furthermore", "consequently", "moreover"]
        informal_indicators = ["gonna", "wanna", "yeah", "ok", "cool"]
        academic_indicators = ["research", "study", "analysis", "hypothesis"]
        
        formal_count = sum(1 for word in formal_indicators if word in text.lower())
        informal_count = sum(1 for word in informal_indicators if word in text.lower())
        academic_count = sum(1 for word in academic_indicators if word in text.lower())
        
        # Determine dominant style
        if academic_count > 0:
            style = "academic"
        elif formal_count > informal_count:
            style = "formal"
        elif informal_count > 0:
            style = "informal"
        else:
            style = "neutral"
        
        return {
            "detected_style": style,
            "formal_indicators": formal_count,
            "informal_indicators": informal_count,
            "academic_indicators": academic_count
        }
    
    def _analyze_tone(self, text: str) -> Dict[str, Any]:
        """Analyze the tone of the text."""
        positive_words = ["good", "great", "excellent", "amazing", "wonderful"]
        negative_words = ["bad", "terrible", "awful", "horrible", "disappointing"]
        
        positive_count = sum(1 for word in positive_words if word in text.lower())
        negative_count = sum(1 for word in negative_words if word in text.lower())
        
        if positive_count > negative_count:
            tone = "positive"
        elif negative_count > positive_count:
            tone = "negative"
        else:
            tone = "neutral"
        
        return {
            "overall_tone": tone,
            "positive_indicators": positive_count,
            "negative_indicators": negative_count
        }
    
    def _get_style_suggestions(self, text: str, target_style: str) -> List[Dict[str, Any]]:
        """Get suggestions to match target style."""
        suggestions = []
        
        if target_style == "formal":
            if any(word in text.lower() for word in ["gonna", "wanna", "yeah"]):
                suggestions.append({
                    "type": "formality",
                    "message": "Replace informal contractions with formal language",
                    "example": "'going to' instead of 'gonna'"
                })
        
        elif target_style == "academic":
            if "I think" in text or "I believe" in text:
                suggestions.append({
                    "type": "academic",
                    "message": "Use more objective language in academic writing",
                    "example": "'The evidence suggests' instead of 'I think'"
                })
        
        return suggestions
    
    def _calculate_complexity(self, text: str) -> float:
        """Calculate text complexity score."""
        words = text.split()
        if not words:
            return 0
        
        # Count syllables (simplified)
        total_syllables = sum(self._count_syllables(word) for word in words)
        avg_syllables = total_syllables / len(words)
        
        # Complexity based on average syllables per word
        return round(min(10, avg_syllables * 2), 1)
    
    def _count_syllables(self, word: str) -> int:
        """Count syllables in a word (simplified)."""
        word = word.lower()
        vowels = "aeiouy"
        syllable_count = 0
        prev_was_vowel = False
        
        for char in word:
            if char in vowels:
                if not prev_was_vowel:
                    syllable_count += 1
                prev_was_vowel = True
            else:
                prev_was_vowel = False
        
        # Handle silent e
        if word.endswith('e') and syllable_count > 1:
            syllable_count -= 1
        
        return max(1, syllable_count)


# Register tools
tool_registry.register_tool(GrammarCheckerTool())
tool_registry.register_tool(StyleAnalyzerTool())
