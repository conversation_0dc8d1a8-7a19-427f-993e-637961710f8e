# Embedding Provider Implementation Template

This document provides a template for implementing new embedding providers in the Xapa AI system.

## Overview

Embedding providers in the Xapa AI system follow a standardized interface defined in `app/embedding/base.py`. Each provider must implement the `EmbeddingProvider` abstract base class.

## Template Structure

```python
from typing import List, Dict, Any, Union
from app.embedding.base import EmbeddingProvider, EmbeddingResponse
from app.core.exceptions import LLMError
import logging

logger = logging.getLogger(__name__)

class YourEmbeddingProvider(EmbeddingProvider):
    """Your embedding provider implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the embedding provider.
        
        Args:
            config: Configuration dictionary containing provider settings
        """
        super().__init__(config)
        # Initialize your provider with configuration
        self.api_key = config.get("api_key")
        self.model = config.get("model")
        self.endpoint = config.get("endpoint")
        # Add other provider-specific initialization
        
    async def generate_embeddings(
        self, 
        texts: Union[str, List[str]], 
        **kwargs
    ) -> EmbeddingResponse:
        """
        Generate embeddings for the given texts.
        
        Args:
            texts: Single text string or list of text strings
            **kwargs: Additional parameters for embedding generation
            
        Returns:
            EmbeddingResponse: Generated embeddings
            
        Raises:
            LLMError: If embedding generation fails
        """
        try:
            # Handle both single text and list of texts
            is_single = isinstance(texts, str)
            text_list = [texts] if is_single else texts
            
            # Implement your embedding generation logic here
            # This should handle:
            # 1. Preparing the texts for your provider's API
            # 2. Making the API call to your provider
            # 3. Processing the response
            # 4. Converting to an EmbeddingResponse object
            
            # Example placeholder implementation:
            embeddings = []
            for i, text in enumerate(text_list):
                # Generate a placeholder embedding (should be actual vector)
                embedding = [0.1 * i + j * 0.01 for j in range(1536)]  # 1536-dimensional vector
                embeddings.append(embedding)
            
            response_data = {
                "embeddings": embeddings[0] if is_single else embeddings,
                "model": self.model,
                "provider": "your_provider"
            }
            
            return EmbeddingResponse(**response_data)
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {str(e)}")
            raise LLMError(f"Your provider error: {str(e)}")
    
    async def validate_connection(self) -> bool:
        """
        Validate the connection to the embedding provider.
        
        Returns:
            bool: True if connection is valid, False otherwise
        """
        try:
            # Implement connection validation logic
            # This might involve making a simple API call to verify credentials
            return True
        except Exception as e:
            logger.error(f"Connection validation failed: {str(e)}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the current model.
        
        Returns:
            Dict[str, Any]: Model information
        """
        return {
            "model": self.model,
            "provider": "your_provider",
            "dimensions": 1536,  # Example dimension count
            "max_sequence_length": 8192  # Example max sequence length
        }

# Factory function for creating provider instances
def create_your_embedding_provider(config: Dict[str, Any]) -> YourEmbeddingProvider:
    """
    Create an instance of your embedding provider.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        YourEmbeddingProvider: Provider instance
    """
    return YourEmbeddingProvider(config)
```

## Registration

To register your provider with the factory, add it to `app/embedding/provider_factory.py`:

```python
# In provider_factory.py
from your_embedding_provider_module import create_your_embedding_provider

# Add to the factory registration
EmbeddingProviderFactory.register_provider("your_provider", create_your_embedding_provider)
```

And update the configuration in `app/core/config.py`:

```python
# In config.py
class Settings(BaseSettings):
    # ... other settings ...
    
    # Your provider settings
    your_provider_api_key: Optional[str] = Field(default=None, env="YOUR_PROVIDER_API_KEY")
    your_provider_model: Optional[str] = Field(default=None, env="YOUR_PROVIDER_MODEL")
    your_provider_endpoint: Optional[str] = Field(default=None, env="YOUR_PROVIDER_ENDPOINT")
```

## Configuration

Add the following environment variables to `.env.example`:

```env
# Your Provider Configuration
YOUR_PROVIDER_API_KEY=your_api_key_here
YOUR_PROVIDER_MODEL=your_model_name
YOUR_PROVIDER_ENDPOINT=your_endpoint_url
```

## Usage

Once implemented and registered, your provider can be used by setting the `EMBEDDING_PROVIDER` environment variable to `"your_provider"`.

## Best Practices

1. **Error Handling**: Always wrap API calls in try/except blocks and raise appropriate `LLMError` exceptions
2. **Logging**: Use the provided logger to log important events and errors
3. **Configuration**: Use the configuration system to manage provider-specific settings
4. **Validation**: Implement connection validation to ensure the provider is properly configured
5. **Batch Processing**: Implement efficient batch processing for multiple texts when possible
6. **Rate Limiting**: Consider implementing rate limiting if your provider has usage limits
7. **Retries**: Implement retry logic for transient failures
8. **Consistent Output**: Ensure embeddings are returned in a consistent format
9. **Dimensions**: Document the embedding dimensions for your model
10. **Performance**: Optimize for performance, especially for large batches of texts

## Example Provider

Here's a simplified example of an embedding provider:

```python
from typing import List, Dict, Any, Union
from app.embedding.base import EmbeddingProvider, EmbeddingResponse
from app.core.exceptions import LLMError
import logging
import hashlib

logger = logging.getLogger(__name__)

class DummyEmbeddingProvider(EmbeddingProvider):
    """Dummy embedding provider for testing."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.model = config.get("model", "dummy-embedder")
        
    async def generate_embeddings(
        self, 
        texts: Union[str, List[str]], 
        **kwargs
    ) -> EmbeddingResponse:
        try:
            is_single = isinstance(texts, str)
            text_list = [texts] if is_single else texts
            
            embeddings = []
            for text in text_list:
                # Generate deterministic "embeddings" based on text hash
                # In a real implementation, this would call an actual embedding API
                text_hash = hashlib.md5(text.encode()).hexdigest()
                embedding = [
                    (ord(c) - ord('0')) / 10.0 if c.isdigit() 
                    else (ord(c.lower()) - ord('a')) / 26.0 
                    for c in text_hash[:128]
                ]
                embeddings.append(embedding)
            
            return EmbeddingResponse(
                embeddings=embeddings[0] if is_single else embeddings,
                model=self.model,
                provider="dummy"
            )
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {str(e)}")
            raise LLMError(f"Dummy provider error: {str(e)}")
    
    async def validate_connection(self) -> bool:
        # Dummy provider is always valid
        return True
    
    def get_model_info(self) -> Dict[str, Any]:
        return {
            "model": self.model,
            "provider": "dummy",
            "dimensions": 128,
            "max_sequence_length": 512
        }

def create_dummy_embedding_provider(config: Dict[str, Any]) -> DummyEmbeddingProvider:
    return DummyEmbeddingProvider(config)