/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    visibility: hidden;
    opacity: 0;
    transition: visibility 0s linear 0.3s, opacity 0.3s ease;
}

.loading-overlay.show {
    visibility: visible;
    opacity: 1;
    transition-delay: 0s;
}

.loading-spinner {
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top: 4px solid #fff;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

.loading-overlay p {
    color: #fff;
    margin-top: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom styles for Xapa AI */

:root {
    --primary-color: #6705bd;
    --primary-light: #8b47d1;
    --primary-lighter: #a86fdb;
    --primary-lightest: #c597e5;
    --primary-dark: #5004a2;
    --primary-darker: #3d0387;
    --primary-darkest: #2a026c;
    --primary-rgb: 103, 5, 189;
    --primary-alpha-10: rgba(103, 5, 189, 0.1);
    --primary-alpha-15: rgba(103, 5, 189, 0.15);
    --primary-alpha-25: rgba(103, 5, 189, 0.25);
    --primary-alpha-50: rgba(103, 5, 189, 0.5);
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    --gradient-primary-subtle: linear-gradient(135deg, var(--primary-alpha-10), var(--primary-alpha-15));
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: 700;
    color: white !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    color: rgba(255, 255, 255, 0.9) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    transform: translateY(-1px);
}

.navbar-custom-primary {
    background: var(--gradient-primary) !important;
    border-bottom: 3px solid var(--primary-dark);
    box-shadow: 0 2px 10px var(--primary-alpha-25);
}

.bg-custom-primary {
    background: var(--gradient-primary) !important;
}

.btn-primary {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    border-width: 2px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-darker);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px var(--primary-alpha-25);
}

.btn-primary:active {
    background: var(--primary-darker);
    border-color: var(--primary-darkest);
    transform: translateY(0);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    border-width: 2px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: var(--gradient-primary);
    border-color: var(--primary-dark);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px var(--primary-alpha-25);
}

.text-primary {
    color: var(--primary-color) !important;
}

.link-primary {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.link-primary:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Card Enhancements */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
    border-radius: 0.75rem;
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 0.75rem 2rem rgba(0, 0, 0, 0.12), 0 0 0 1px var(--primary-alpha-15);
    transform: translateY(-2px);
}

.card-header {
    background: var(--gradient-primary-subtle);
    border-bottom: 1px solid var(--primary-alpha-15);
    font-weight: 600;
    color: var(--primary-dark);
}

.card-header.bg-primary {
    background: var(--gradient-primary) !important;
    color: white;
    border-bottom: 1px solid var(--primary-dark);
}

.card-primary {
    border: 2px solid var(--primary-color);
}

.card-primary .card-header {
    background: var(--gradient-primary);
    color: white;
    border-bottom: 1px solid var(--primary-dark);
}

/* Tool Selection Styles */
.tool-item {
    border: 2px solid #dee2e6;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.tool-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary-subtle);
    transition: left 0.3s ease;
    z-index: -1;
}

.tool-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px var(--primary-alpha-15);
}

.tool-item:hover::before {
    left: 0;
}

.tool-item.selected {
    border-color: var(--primary-color);
    background: var(--primary-alpha-10);
    box-shadow: 0 0 0 3px var(--primary-alpha-15);
}

.tool-item.selected::before {
    left: 0;
}

.tool-category {
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.75rem;
    padding: 0.75rem 1rem;
    background: var(--gradient-primary-subtle);
    border-radius: 0.5rem;
    border-left: 4px solid var(--primary-color);
    position: relative;
}

.tool-category::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-primary);
}

.tool-name {
    font-weight: 600;
    color: var(--dark-color);
}

.tool-description {
    color: var(--secondary-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.tool-parameters {
    font-size: 0.75rem;
    color: var(--info-color);
    margin-top: 0.5rem;
}

/* Status Badges */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-active {
    background-color: var(--success-color);
    color: white;
}

.status-inactive {
    background-color: var(--secondary-color);
    color: white;
}

/* Table Enhancements */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table th {
    border-top: none;
    font-weight: 700;
    color: var(--primary-dark);
    background: var(--gradient-primary-subtle);
    padding: 1rem 0.75rem;
    position: relative;
}

.table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
}

.table-hover tbody tr {
    transition: all 0.2s ease;
}

.table-hover tbody tr:hover {
    background: var(--primary-alpha-10);
    transform: scale(1.01);
    box-shadow: 0 2px 8px var(--primary-alpha-15);
}

.table-primary {
    --bs-table-bg: var(--primary-alpha-10);
    --bs-table-hover-bg: var(--primary-alpha-15);
}

/* Button Enhancements */
.btn {
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-sm {
    font-size: 0.8rem;
}

/* Table Actions Button Group */
.table td .btn-group {
    white-space: nowrap;
    display: inline-flex;
    vertical-align: middle;
}

.table td .btn-group .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    line-height: 1;
}

.table td .btn-group .btn i {
    margin: 0;
    line-height: 1;
}

/* Form Enhancements */
.form-label {
    font-weight: 700;
    color: var(--primary-dark);
    margin-bottom: 0.75rem;
}

.form-control {
    border-radius: 0.5rem;
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem var(--primary-alpha-25), 0 4px 15px var(--primary-alpha-15);
    transform: translateY(-1px);
}

.form-control:hover:not(:focus) {
    border-color: var(--primary-light);
}

.form-select {
    border-radius: 0.5rem;
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem var(--primary-alpha-25);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.25rem var(--primary-alpha-25);
}

.form-text {
    font-size: 0.875rem;
    color: var(--secondary-color);
    font-weight: 500;
}

.input-group .form-control:focus {
    z-index: 5;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

.spinner-border-primary {
    color: var(--primary-color);
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 0.75rem;
    border-left: 4px solid transparent;
    font-weight: 500;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.alert-primary {
    background: var(--primary-alpha-10);
    color: var(--primary-dark);
    border-left-color: var(--primary-color);
}

.alert-info {
    border-left-color: var(--info-color);
}

.alert-success {
    border-left-color: var(--success-color);
}

.alert-warning {
    border-left-color: var(--warning-color);
}

.alert-danger {
    border-left-color: var(--danger-color);
}

/* Chat Interface Styles */
.chat-container {
    height: 500px;
    overflow-y: auto;
    border: 2px solid var(--primary-alpha-15);
    border-radius: 1rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    position: relative;
}

.chat-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 1rem 1rem 0 0;
}

.message {
    margin-bottom: 1.5rem;
    padding: 1rem 1.25rem;
    border-radius: 1.25rem;
    max-width: 80%;
    position: relative;
    animation: messageSlideIn 0.3s ease-out;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    background: var(--gradient-primary);
    color: white;
    margin-left: auto;
    text-align: right;
    box-shadow: 0 4px 15px var(--primary-alpha-25);
}


.message.assistant {
    background: linear-gradient(135deg, #ffffff 0%, var(--light-color) 100%);
    color: var(--dark-color);
    margin-right: auto;
    border: 1px solid var(--primary-alpha-15);
}


.message.system {
    background: var(--gradient-primary-subtle);
    color: var(--primary-dark);
    margin: 0 auto;
    text-align: center;
    font-style: italic;
    border: 1px solid var(--primary-alpha-15);
    font-weight: 500;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-top: 0.5rem;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    /* Session list responsive fixes */
    .session-name {
        font-size: 0.875rem;
        max-width: none;
    }
    
    .role-badge {
        font-size: 0.65rem;
        padding: 0.2rem 0.4rem;
        white-space: nowrap;
    }
    
    .delete-session-btn {
        min-width: 32px;
        padding: 0.25rem 0.4rem;
        font-size: 0.75rem;
    }
    
    .list-group-item {
        padding: 0.5rem 0.75rem;
    }
    
    .session-item-clickable {
        min-height: auto;
    }
}

/* Extra small screens - stack everything vertically */
@media (max-width: 480px) {
    .session-name {
        font-size: 0.8rem;
        margin-bottom: 0.5rem;
    }
    
    .role-badge {
        font-size: 0.6rem;
        padding: 0.15rem 0.3rem;
        margin-right: 0.5rem;
    }
    
    .delete-session-btn {
        min-width: 28px;
        padding: 0.2rem 0.3rem;
        font-size: 0.7rem;
    }
    
    .list-group-item {
        padding: 0.4rem 0.6rem;
    }
}

/* Session item styling */
.session-item-clickable {
    transition: background-color 0.2s ease;
    border-radius: 0.375rem;
}

.session-item-clickable:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.session-name {
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.2;
}

.role-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.delete-session-btn {
    border: none;
    background: none;
    color: #dc3545;
    padding: 0.375rem 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.delete-session-btn:hover {
    background-color: #dc3545;
    color: white;
    opacity: 1;
    transform: scale(1.05);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.border-dashed {
    border-style: dashed !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.border-primary-light {
    border-color: var(--primary-light) !important;
}

.bg-primary-light {
    background-color: var(--primary-light) !important;
}

.bg-primary-alpha-10 {
    background-color: var(--primary-alpha-10) !important;
}

.bg-gradient-primary {
    background: var(--gradient-primary) !important;
}

.text-primary-dark {
    color: var(--primary-dark) !important;
}

.text-primary-light {
    color: var(--primary-light) !important;
}

.shadow-primary {
    box-shadow: 0 4px 15px var(--primary-alpha-25) !important;
}

.shadow-primary-lg {
    box-shadow: 0 10px 30px var(--primary-alpha-25) !important;
}

/* Badge Enhancements */
.badge-primary {
    background: var(--gradient-primary) !important;
    color: white;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 0.75rem;
}

.badge-outline-primary {
    background: transparent !important;
    color: var(--primary-color) !important;
    border: 2px solid var(--primary-color);
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 0.75rem;
}

/* Progress Bar Enhancements */
.progress {
    border-radius: 0.75rem;
    overflow: hidden;
    height: 0.75rem;
    background: var(--primary-alpha-10);
}

.progress-bar {
    background: var(--gradient-primary);
    transition: width 0.6s ease;
}

/* Navigation Enhancements */
.nav-pills .nav-link {
    border-radius: 0.75rem;
    font-weight: 500;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
}

.nav-pills .nav-link.active {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 4px 15px var(--primary-alpha-25);
}

.nav-pills .nav-link:hover:not(.active) {
    background: var(--primary-alpha-10);
    color: var(--primary-color);
}

/* Dropdown Enhancements */
.dropdown-menu {
    border: 2px solid var(--primary-alpha-15);
    border-radius: 0.75rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.dropdown-item {
    font-weight: 500;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: var(--primary-alpha-10);
    color: var(--primary-color);
}

.dropdown-item.active {
    background: var(--gradient-primary);
    color: white;
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #8b47d1;
        --primary-light: #a86fdb;
        --primary-lighter: #c597e5;
        --primary-dark: #6705bd;
        --primary-darker: #5004a2;
        --light-color: #2d2d2d;
        --dark-color: #ffffff;
        --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    }
    
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .card-header {
        background: rgba(139, 71, 209, 0.1);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .table {
        color: #ffffff;
    }
    
    .table th {
        background: rgba(139, 71, 209, 0.1);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .form-control {
        background-color: #2d2d2d;
        border-color: rgba(255, 255, 255, 0.2);
        color: #ffffff;
    }
    
    .form-control:focus {
        background-color: #2d2d2d;
        border-color: var(--primary-color);
        color: #ffffff;
    }
    
    .tool-item {
        background-color: #2d2d2d;
        border-color: rgba(255, 255, 255, 0.2);
        color: #ffffff;
    }
    
    .message.assistant {
        background: #2d2d2d;
        color: #ffffff;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .chat-container {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        border-color: rgba(139, 71, 209, 0.3);
    }
}