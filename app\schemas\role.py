from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
import uuid
import re
import base64


class RoleBase(BaseModel):
    """Base role schema."""
    display_name: str = Field(..., min_length=1, max_length=100, description="User-friendly role name")
    description: Optional[str] = Field(None, description="Role description")
    system_prompt: str = Field(..., min_length=1, description="System prompt for the AI role")
    tools: List[str] = Field(default_factory=list, description="List of available tools for this role")
    config: Dict[str, Any] = Field(default_factory=dict, description="Role configuration (temperature, max_tokens, etc.)")
    image_url: Optional[str] = Field(None, description="Azure Blob Storage image URL")
    is_active: bool = Field(default=True, description="Whether the role is active")


class RoleCreate(RoleBase):
    """Schema for creating a new role."""
    name: str = Field(..., min_length=1, max_length=50, description="Unique role identifier")
    image_data: Optional[str] = Field(None, description="Base64 encoded image data or data URL")
    
    @validator('name')
    def validate_name(cls, v):
        """Validate role name constraints."""
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Role name must contain only alphanumeric characters, hyphens, and underscores')
        return v.lower()
    
    @validator('image_data')
    def validate_image_data(cls, v):
        """Validate base64 image data."""
        if v is not None and v.strip():
            # Check if it's a data URL or raw base64
            if v.startswith('data:'):
                try:
                    header, data = v.split(',', 1)
                    base64.b64decode(data, validate=True)
                except Exception:
                    raise ValueError('Invalid base64 image data in data URL')
            else:
                try:
                    base64.b64decode(v, validate=True)
                except Exception:
                    raise ValueError('Invalid base64 image data')
        return v
    
    @validator('tools')
    def validate_tools(cls, v):
        """Validate tools list."""
        if not isinstance(v, list):
            raise ValueError('Tools must be a list')
        return [tool.strip() for tool in v if tool.strip()]
    
    @validator('config')
    def validate_config(cls, v):
        """Validate configuration dictionary."""
        if not isinstance(v, dict):
            raise ValueError('Config must be a dictionary')
        
        # Validate common config fields if present
        if 'temperature' in v:
            temp = v['temperature']
            if not isinstance(temp, (int, float)) or temp < 0 or temp > 2:
                raise ValueError('Temperature must be a number between 0 and 2')
        
        if 'max_tokens' in v:
            max_tokens = v['max_tokens']
            if not isinstance(max_tokens, int) or max_tokens < 1:
                raise ValueError('Max tokens must be a positive integer')
        
        return v
    
    @validator('image_data')
    def validate_image_data(cls, v):
        """Validate base64 image data."""
        if v is None:
            return v
        
        if not isinstance(v, str):
            raise ValueError('Image data must be a string')
        
        # Check if it's a valid base64 data URL
        if v.startswith('data:image/'):
            try:
                # Extract the base64 part after the comma
                header, data = v.split(',', 1)
                # Validate it's a supported image format
                if not any(fmt in header for fmt in ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']):
                    raise ValueError('Unsupported image format. Use JPEG, PNG, GIF, or WebP')
                # Try to decode to validate base64
                base64.b64decode(data)
                return v
            except (ValueError, Exception):
                raise ValueError('Invalid base64 image data')
        else:
            # If not a data URL, assume it's raw base64
            try:
                base64.b64decode(v)
                return v
            except Exception:
                raise ValueError('Invalid base64 image data')
        
        return v


class RoleUpdate(BaseModel):
    """Schema for updating an existing role."""
    display_name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    system_prompt: Optional[str] = Field(None, min_length=1)
    tools: Optional[List[str]] = None
    config: Optional[Dict[str, Any]] = None
    image_url: Optional[str] = Field(None, description="Azure Blob Storage image URL")
    image_data: Optional[str] = Field(None, description="Base64 encoded image data or data URL")
    is_active: Optional[bool] = None
    
    @validator('image_data')
    def validate_image_data(cls, v):
        """Validate base64 image data."""
        if v is not None and v.strip():
            # Check if it's a data URL or raw base64
            if v.startswith('data:'):
                try:
                    header, data = v.split(',', 1)
                    base64.b64decode(data, validate=True)
                except Exception:
                    raise ValueError('Invalid base64 image data in data URL')
            else:
                try:
                    base64.b64decode(v, validate=True)
                except Exception:
                    raise ValueError('Invalid base64 image data')
        return v
    
    @validator('tools')
    def validate_tools(cls, v):
        """Validate tools list."""
        if v is not None:
            if not isinstance(v, list):
                raise ValueError('Tools must be a list')
            return [tool.strip() for tool in v if tool.strip()]
        return v
    
    @validator('config')
    def validate_config(cls, v):
        """Validate configuration dictionary."""
        if v is not None:
            if not isinstance(v, dict):
                raise ValueError('Config must be a dictionary')
            
            # Validate common config fields if present
            if 'temperature' in v:
                temp = v['temperature']
                if not isinstance(temp, (int, float)) or temp < 0 or temp > 2:
                    raise ValueError('Temperature must be a number between 0 and 2')
            
            if 'max_tokens' in v:
                max_tokens = v['max_tokens']
                if not isinstance(max_tokens, int) or max_tokens < 1:
                    raise ValueError('Max tokens must be a positive integer')
        
        return v
    
    @validator('image_data')
    def validate_image_data(cls, v):
        """Validate base64 image data."""
        if v is None:
            return v
        
        if not isinstance(v, str):
            raise ValueError('Image data must be a string')
        
        # Check if it's a valid base64 data URL
        if v.startswith('data:image/'):
            try:
                # Extract the base64 part after the comma
                header, data = v.split(',', 1)
                # Validate it's a supported image format
                if not any(fmt in header for fmt in ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']):
                    raise ValueError('Unsupported image format. Use JPEG, PNG, GIF, or WebP')
                # Try to decode to validate base64
                base64.b64decode(data)
                return v
            except (ValueError, Exception):
                raise ValueError('Invalid base64 image data')
        else:
            # If not a data URL, assume it's raw base64
            try:
                base64.b64decode(v)
                return v
            except Exception:
                raise ValueError('Invalid base64 image data')
        
        return v


class RoleResponse(RoleBase):
    """Schema for role response."""
    id: Union[str, uuid.UUID]
    name: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
        # Automatically convert UUID to string
        json_encoders = {
            uuid.UUID: str
        }
    
    def model_dump(self, **kwargs):
        """Override to ensure UUID is always converted to string."""
        data = super().model_dump(**kwargs)
        if isinstance(data.get('id'), uuid.UUID):
            data['id'] = str(data['id'])
        return data


class RoleListResponse(BaseModel):
    """Schema for roles list response."""
    roles: List[RoleResponse]
    total: int = Field(..., description="Total number of roles")



