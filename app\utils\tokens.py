"""
Token counting utilities for fallback when LLM providers don't return usage information.
"""
import tiktoken
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)


def count_tokens(text: str, model: str = "gpt-4") -> int:
    """
    Count tokens in a text string using tiktoken.
    
    Args:
        text: The text to count tokens for
        model: The model name to use for encoding (defaults to gpt-4)
        
    Returns:
        int: Number of tokens in the text
    """
    try:
        encoding = tiktoken.encoding_for_model(model)
        return len(encoding.encode(text))
    except KeyError:
        # Fallback to cl100k_base encoding if model not found
        logger.warning(f"Model {model} not found in tiktoken, using cl100k_base encoding")
        encoding = tiktoken.get_encoding("cl100k_base")
        return len(encoding.encode(text))
    except Exception as e:
        logger.error(f"Error counting tokens: {e}")
        return 0


def estimate_usage(messages: List[Dict[str, Any]], response_content: str, model: str = "gpt-4") -> Dict[str, int]:
    """
    Estimate token usage for a conversation when LLM provider doesn't return usage.
    
    Args:
        messages: List of conversation messages
        response_content: The generated response content
        model: The model name to use for encoding
        
    Returns:
        Dict[str, int]: Estimated usage with prompt_tokens, completion_tokens, total_tokens
    """
    try:
        # Count prompt tokens (all messages except the last assistant response)
        prompt_text = ""
        for msg in messages:
            if msg.get("role") and msg.get("content"):
                prompt_text += f"{msg['role']}: {msg['content']}\n"
        
        prompt_tokens = count_tokens(prompt_text, model)
        completion_tokens = count_tokens(response_content, model)
        total_tokens = prompt_tokens + completion_tokens
        
        return {
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "total_tokens": total_tokens
        }
    except Exception as e:
        logger.error(f"Error estimating token usage: {e}")
        return {
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0
        }


def get_model_encoding_name(model: str) -> str:
    """
    Get the encoding name for a given model.
    
    Args:
        model: The model name
        
    Returns:
        str: The encoding name to use
    """
    # Map common models to their encodings
    model_encodings = {
        "gpt-4": "cl100k_base",
        "gpt-4-32k": "cl100k_base", 
        "gpt-3.5-turbo": "cl100k_base",
        "gpt-3.5-turbo-16k": "cl100k_base",
        "text-davinci-003": "p50k_base",
        "text-davinci-002": "p50k_base",
        "code-davinci-002": "p50k_base",
    }
    
    # Check for partial matches (e.g., gpt-4-0613, gpt-3.5-turbo-0613)
    for known_model, encoding in model_encodings.items():
        if model.startswith(known_model):
            return encoding
    
    # Default fallback
    return "cl100k_base"