{% extends "base.html" %}

{% block title %}{{ 'Edit' if role else 'Create' }} Role - Xapa AI{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-{{ 'edit' if role else 'plus' }} me-2"></i>
                        {{ 'Edit' if role else 'Create' }} Role
                    </h3>
                </div>
                <div class="card-body">
                    <form id="roleForm">
                        <!-- Basic Information and Image Layout -->
                        <div class="row mb-4">
                            <!-- Left Column: Basic Information -->
                            <div class="col-md-8">
                                <!-- Role Name -->
                                <div class="mb-3">
                                    <label for="name" class="form-label">Role Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{{ role.name if role else '' }}" 
                                           {{ 'readonly' if role else '' }} required>
                                    <div class="form-text">Unique identifier (lowercase, alphanumeric, hyphens, underscores)</div>
                                </div>

                                <!-- Display Name -->
                                <div class="mb-3">
                                    <label for="display_name" class="form-label">Display Name *</label>
                                    <input type="text" class="form-control" id="display_name" name="display_name" 
                                           value="{{ role.display_name if role else '' }}" required>
                                </div>

                                <!-- Description -->
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="4">{{ role.description if role else '' }}</textarea>
                                </div>
                            </div>

                            <!-- Right Column: Role Image -->
                            <div class="col-md-4 d-flex flex-column">
                                <label for="role_image" class="form-label">Role Image</label>
                                <div class="card flex-grow-1">
                                    <div class="card-body d-flex flex-column">
                                        <!-- Image Preview -->
                                        <div class="text-center mb-3">
                                            <div id="imagePreview" class="mb-3">
                                            {% if role and role.image_url and role.image_url != 'None' %}
                                                <img src="{{ role.image_url }}" alt="Role Image" class="img-fluid rounded" style="max-width: 150px; max-height: 150px; object-fit: cover;" onerror="this.onerror=null; this.outerHTML = '<div class=\'border rounded d-flex align-items-center justify-content-center bg-light\' style=\'width: 150px; height: 150px; margin: 0 auto;\'><i class=\'fas fa-user-circle fa-4x text-muted\'></i></div>';">
                                            {% else %}
                                                <div class="border rounded d-flex align-items-center justify-content-center bg-light" style="width: 150px; height: 150px; margin: 0 auto;">
                                                    <i class="fas fa-user-circle fa-4x text-muted"></i>
                                                </div>
                                            {% endif %}
                                        </div>
                                        </div>

                                        <!-- File Input with Tooltip -->
                                        <div class="mb-3">
                                            <input type="file" class="form-control" id="role_image" name="role_image" accept="image/*" onchange="previewImage(event)"
                                                   data-bs-toggle="tooltip" data-bs-placement="left" data-bs-html="true"
                                                   title="<strong>Image Guidelines:</strong><br/>
                                                          • Supported formats: PNG, JPG, GIF, WebP<br/>
                                                          • Maximum file size: 5MB<br/>
                                                          • Recommended size: 150x150 pixels<br/>
                                                          • Images stored as base64 in database">
                                            <div class="form-text small">
                                                <i class="fas fa-info-circle text-muted me-1"></i>
                                                Hover upload button for guidelines
                                            </div>
                                        </div>

                                        <!-- Remove Button -->
                                        {% if role and role.image_url %}
                                        <button type="button" class="btn btn-md btn-outline-danger" onclick="removeImage()">
                                            <i class="fas fa-trash me-1"></i>
                                            Remove Image
                                        </button>
                                        {% endif %}
                                    </div>
                                </div>
                                <input type="hidden" id="image_url" name="image_url" value="{{ role.image_url if role and role.image_url else '' }}">
                            </div>
                        </div>

                        <!-- System Prompt -->
                        <div class="mb-3">
                            <label for="system_prompt" class="form-label">System Prompt *</label>
                            <textarea class="form-control" id="system_prompt" name="system_prompt" rows="6" required>{{ role.system_prompt if role else '' }}</textarea>
                            <div class="form-text">Instructions that define the AI agent's behavior and personality</div>
                        </div>

                        <!-- Tools Selection -->
                        <div class="mb-3">
                            <label class="form-label">Available Tools</label>
                            <div class="card">
                                <div class="card-header">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h6 class="mb-0">Select tools for this role</h6>
                                        </div>
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshTools()">
                                                <i class="fas fa-refresh me-1"></i>
                                                Refresh
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <input type="text" class="form-control mb-3" id="toolSearch" placeholder="Search tools..." onkeyup="filterTools()">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="selectAllTools" onchange="toggleAllTools()">
                                                <label class="form-check-label" for="selectAllTools">
                                                    <strong>Select All</strong>
                                                </label>
                                            </div>
                                            <div id="toolCategories">
                                                <!-- Tool categories will be loaded here -->
                                            </div>
                                        </div>
                                        <div class="col-md-8">
                                            <div id="toolsList">
                                                <!-- Tools will be loaded here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Configuration -->
                        <div class="mb-3">
                            <label class="form-label">Configuration</label>
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="temperature" class="form-label">Temperature</label>
                                            <input type="number" class="form-control" id="temperature" name="temperature" 
                                                   min="0" max="2" step="0.1" value="{{ role.config.temperature if role and role.config.temperature else '0.7' }}">
                                            <div class="form-text">Controls randomness (0.0 = deterministic, 2.0 = very random)</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="max_tokens" class="form-label">Max Tokens</label>
                                            <input type="number" class="form-control" id="max_tokens" name="max_tokens" 
                                                   min="1" max="4096" value="{{ role.config.max_tokens if role and role.config.max_tokens else '1000' }}">
                                            <div class="form-text">Maximum response length</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Status -->
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       {{ 'checked' if not role or role.is_active else '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active Role
                                </label>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="/roles" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                Back to Roles
                            </a>
                            <div>
                                {% if role %}
                                <button type="button" class="btn btn-info me-2" onclick="testRole()">
                                    <i class="fas fa-flask me-1"></i>
                                    Test Role
                                </button>
                                {% endif %}
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    {{ 'Update' if role else 'Create' }} Role
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Saving role...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    window.roleData = {{ role | tojson | safe if role else 'null' }};
</script>
<script src="/static/js/role-form.js"></script>
{% endblock %}
