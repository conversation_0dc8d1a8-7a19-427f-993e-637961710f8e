"""Add image_url to roles table

Revision ID: 002_add_image_url
Revises: 001_add_image_data
Create Date: 2025-01-22 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '002_add_image_url'
down_revision = '001_add_image_data'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add image_url column to roles table for Azure Blob Storage URLs
    op.add_column('roles', sa.Column('image_url', sa.String(500), nullable=True))


def downgrade() -> None:
    # Remove image_url column
    op.drop_column('roles', 'image_url')