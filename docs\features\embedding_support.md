# Embedding Support

The Xapa AI now includes comprehensive embedding support, allowing you to generate vector embeddings for text, compute similarities, perform semantic search, and cluster documents. The embedding system follows the same provider pattern as the LLM system, supporting multiple embedding providers.

## Features

- **Multiple Provider Support**: OpenAI and Azure OpenAI embedding providers
- **Flexible API**: Single text, batch text, and streaming embedding generation
- **Similarity Operations**: Compute similarity between texts using various metrics
- **Semantic Search**: Find most similar texts from a collection
- **Text Clustering**: Group similar texts using K-means clustering
- **Provider Management**: Easy switching between embedding providers
- **Authentication**: Secure API access with JWT authentication

## Supported Providers

### 1. Azure OpenAI Embeddings (Recommended)
- **Models**: text-embedding-3-small, text-embedding-3-large, text-embedding-ada-002
- **Enterprise Features**: Data residency, private endpoints, compliance
- **Configuration**: Requires Azure OpenAI resource and deployment

### 2. OpenAI Embeddings
- **Models**: text-embedding-3-small, text-embedding-3-large, text-embedding-ada-002
- **Direct API**: Uses OpenAI's public API
- **Configuration**: Requires OpenAI API key

## Configuration

### Environment Variables

Add these variables to your `.env` file:

```env
# Embedding Provider Configuration
EMBEDDING_PROVIDER=azure_openai

# OpenAI Embeddings
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# Azure OpenAI Embeddings
AZURE_OPENAI_EMBEDDING_MODEL=text-embedding-3-small
```

### Azure OpenAI Setup

1. Create an Azure OpenAI resource
2. Deploy an embedding model (e.g., text-embedding-3-small)
3. Configure the deployment name in `AZURE_OPENAI_EMBEDDING_MODEL`
4. Use the same API key and endpoint as your chat models

### OpenAI Setup

1. Get an OpenAI API key
2. Set `EMBEDDING_PROVIDER=openai`
3. Configure the model in `OPENAI_EMBEDDING_MODEL`

## API Endpoints

All embedding endpoints require authentication and are available under `/api/v1/embeddings/`.

### 1. Create Embeddings

**POST** `/api/v1/embeddings/`

Generate embeddings for single or multiple texts.

```json
{
  "input": "Hello, world!",
  "model": "text-embedding-3-small",
  "encoding_format": "float",
  "dimensions": 1536
}
```

**Response:**
```json
{
  "object": "list",
  "data": [
    {
      "object": "embedding",
      "embedding": [0.1, 0.2, ...],
      "index": 0
    }
  ],
  "model": "text-embedding-3-small",
  "usage": {
    "prompt_tokens": 3,
    "total_tokens": 3
  }
}
```

### 2. Compute Similarity

**POST** `/api/v1/embeddings/similarity`

Compute similarity between two texts.

```json
{
  "text1": "Hello, world!",
  "text2": "Hi there!",
  "similarity_metric": "cosine",
  "model": "text-embedding-3-small"
}
```

**Response:**
```json
{
  "similarity": 0.85,
  "metric": "cosine",
  "model": "text-embedding-3-small"
}
```

### 3. Similarity Search

**POST** `/api/v1/embeddings/search`

Find most similar texts from a collection.

```json
{
  "query": "machine learning",
  "candidates": [
    "artificial intelligence",
    "deep learning",
    "cooking recipes",
    "neural networks"
  ],
  "top_k": 3,
  "similarity_metric": "cosine"
}
```

**Response:**
```json
{
  "query": "machine learning",
  "results": [
    {
      "text": "deep learning",
      "similarity": 0.92,
      "index": 1
    },
    {
      "text": "neural networks",
      "similarity": 0.88,
      "index": 3
    },
    {
      "text": "artificial intelligence",
      "similarity": 0.85,
      "index": 0
    }
  ],
  "metric": "cosine",
  "model": "text-embedding-3-small"
}
```

### 4. Text Clustering

**POST** `/api/v1/embeddings/cluster`

Cluster texts using K-means clustering.

```json
{
  "texts": [
    "machine learning",
    "deep learning",
    "cooking pasta",
    "neural networks",
    "baking bread"
  ],
  "n_clusters": 2
}
```

**Response:**
```json
{
  "clusters": [
    {
      "cluster_id": 0,
      "texts": [
        {"text": "machine learning", "original_index": 0},
        {"text": "deep learning", "original_index": 1},
        {"text": "neural networks", "original_index": 3}
      ]
    },
    {
      "cluster_id": 1,
      "texts": [
        {"text": "cooking pasta", "original_index": 2},
        {"text": "baking bread", "original_index": 4}
      ]
    }
  ],
  "n_clusters": 2,
  "model": "text-embedding-3-small"
}
```

### 5. Provider Information

**GET** `/api/v1/embeddings/provider/info`

Get current provider information and available providers.

**GET** `/api/v1/embeddings/provider/validate`

Validate that the current provider is working correctly.

## Usage Examples

### Python Client Example

```python
import httpx
import asyncio

async def embed_text(text: str, token: str):
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/api/v1/embeddings/",
            json={"input": text},
            headers={"Authorization": f"Bearer {token}"}
        )
        return response.json()

async def find_similar(query: str, candidates: list, token: str):
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/api/v1/embeddings/search",
            json={
                "query": query,
                "candidates": candidates,
                "top_k": 3
            },
            headers={"Authorization": f"Bearer {token}"}
        )
        return response.json()

# Usage
token = "your_jwt_token"
embedding = await embed_text("Hello, world!", token)
similar = await find_similar("AI", ["machine learning", "cooking", "sports"], token)
```

### JavaScript/Node.js Example

```javascript
const axios = require('axios');

async function embedText(text, token) {
  const response = await axios.post(
    'http://localhost:8000/api/v1/embeddings/',
    { input: text },
    { headers: { Authorization: `Bearer ${token}` } }
  );
  return response.data;
}

async function findSimilar(query, candidates, token) {
  const response = await axios.post(
    'http://localhost:8000/api/v1/embeddings/search',
    { query, candidates, top_k: 3 },
    { headers: { Authorization: `Bearer ${token}` } }
  );
  return response.data;
}
```

## Similarity Metrics

The system supports three similarity metrics:

1. **Cosine Similarity** (default): Measures the cosine of the angle between vectors
   - Range: -1 to 1 (1 = identical, 0 = orthogonal, -1 = opposite)
   - Best for: General text similarity

2. **Dot Product**: Measures the dot product of vectors
   - Range: Depends on vector magnitudes
   - Best for: When vector magnitudes matter

3. **Euclidean Distance**: Measures the negative Euclidean distance
   - Range: Negative values (higher = more similar)
   - Best for: When absolute distances matter

## Best Practices

1. **Model Selection**: Use text-embedding-3-small for most use cases, text-embedding-3-large for higher accuracy
2. **Batch Processing**: Use batch embedding for multiple texts to reduce API calls
3. **Caching**: Cache embeddings for frequently used texts
4. **Preprocessing**: Clean and normalize text before embedding
5. **Similarity Thresholds**: Establish appropriate similarity thresholds for your use case

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- **400 Bad Request**: Invalid input or embedding provider error
- **401 Unauthorized**: Missing or invalid authentication token
- **500 Internal Server Error**: Server-side errors

## Dependencies

For clustering functionality, install scikit-learn:

```bash
pip install scikit-learn
```

## Migration from Other Systems

If you're migrating from other embedding systems:

1. Update your configuration to use the new provider system
2. Replace direct API calls with the new endpoint structure
3. Update authentication to use JWT tokens
4. Test embedding compatibility with your existing vectors
