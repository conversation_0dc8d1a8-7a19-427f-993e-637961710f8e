import uuid
from datetime import datetime
from sqlalchemy import Column, String, DateTime, ForeignKey, Text, Integer
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.orm import relationship
from .base import Base


class Message(Base):
    """Message model for storing chat messages."""
    
    __tablename__ = "messages"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(UUID(as_uuid=True), ForeignKey("sessions.id"), nullable=False)
    role = Column(String(20), nullable=False)  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False)
    message_metadata = Column(JSON, default={})  # Store additional data like tokens, tools used, etc.
    prompt_tokens = Column(Integer, nullable=True)  # Number of tokens in the prompt
    completion_tokens = Column(Integer, nullable=True)  # Number of tokens in the completion
    total_tokens = Column(Integer, nullable=True)  # Total number of tokens
    sequence_number = Column(Integer, nullable=False)  # Order within session
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    session = relationship("Session", back_populates="messages")
    
    def __repr__(self):
        return f"<Message(id={self.id}, role='{self.role}', session_id={self.session_id})>"
    
    def to_dict(self):
        """Convert message to dictionary."""
        return {
            "id": str(self.id),
            "session_id": str(self.session_id),
            "role": self.role,
            "content": self.content,
            "metadata": self.message_metadata,
            "prompt_tokens": self.prompt_tokens,
            "completion_tokens": self.completion_tokens,
            "total_tokens": self.total_tokens,
            "sequence_number": self.sequence_number,
            "created_at": self.created_at.isoformat()
        }
    
    @classmethod
    def create_user_message(cls, session_id: uuid.UUID, content: str, sequence_number: int):
        """Create a user message."""
        return cls(
            session_id=session_id,
            role="user",
            content=content,
            sequence_number=sequence_number
        )
    
    @classmethod
    def create_assistant_message(cls, session_id: uuid.UUID, content: str, sequence_number: int, metadata: dict = None, prompt_tokens: int = None, completion_tokens: int = None, total_tokens: int = None):
        """Create an assistant message."""
        return cls(
            session_id=session_id,
            role="assistant",
            content=content,
            sequence_number=sequence_number,
            message_metadata=metadata or {},
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=total_tokens
        )