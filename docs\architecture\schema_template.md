# Schema Implementation Template

This document provides a template for implementing new schemas in the Xapa AI system.

## Overview

Schemas in the Xapa AI system are implemented using Pydantic models and are used for API request/response validation. Each schema should be defined in the appropriate file in the `app/schemas/` directory.

## Template Structure

```python
from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
import uuid

class YourSchemaBase(BaseModel):
    """Base schema with common fields."""
    
    # Define your base fields here
    name: str = Field(..., min_length=1, max_length=100, description="Name of the entity")
    description: Optional[str] = Field(None, description="Description of the entity")
    
    # Add more fields as needed
    # Example:
    # status: str = Field(..., description="Status of the entity")
    # config: Dict[str, Any] = Field(default_factory=dict, description="Configuration data")

class YourSchemaCreate(YourSchemaBase):
    """Schema for creating a new entity."""
    
    # Add fields that are only required for creation
    # Example:
    # initial_value: int = Field(..., description="Initial value for the entity")

class YourSchemaUpdate(BaseModel):
    """Schema for updating an existing entity."""
    
    # All fields should be optional for updates
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    
    # Add more optional fields as needed
    # Example:
    # status: Optional[str] = None
    # config: Optional[Dict[str, Any]] = None

class YourSchemaResponse(YourSchemaBase):
    """Schema for entity response."""
    
    # Add fields that are included in responses but not in requests
    id: Union[str, uuid.UUID]
    created_at: datetime
    updated_at: datetime
    
    # Add more response-specific fields as needed
    # Example:
    # owner_id: str
    
    class Config:
        from_attributes = True
        # Automatically convert UUID to string
        json_encoders = {
            uuid.UUID: str
        }
    
    def model_dump(self, **kwargs):
        """Override to ensure UUID is always converted to string."""
        data = super().model_dump(**kwargs)
        if isinstance(data.get('id'), uuid.UUID):
            data['id'] = str(data['id'])
        return data

class YourSchemaListResponse(BaseModel):
    """Schema for list of entities response."""
    
    items: List[YourSchemaResponse]
    total: int = Field(..., description="Total number of items")
    
    # Add pagination fields if needed
    # Example:
    # page: int
    # page_size: int
```

## File Location

Create your schema file in the `app/schemas/` directory:
```
app/schemas/your_schema.py
```

If you're adding a schema for an existing module, add it to the appropriate existing file:
```
app/schemas/auth.py          # Authentication schemas
app/schemas/chat.py          # Chat schemas
app/schemas/embedding.py     # Embedding schemas
app/schemas/role.py          # Role schemas
app/schemas/session.py       # Session schemas
app/schemas/tool.py          # Tool schemas
```

## Usage in API Endpoints

```python
from fastapi import APIRouter, Depends
from app.schemas.your_schema import YourSchemaCreate, YourSchemaResponse, YourSchemaUpdate

router = APIRouter()

@router.post("/", response_model=YourSchemaResponse)
async def create_entity(entity_data: YourSchemaCreate):
    """Create a new entity."""
    # Implementation here
    pass

@router.get("/{entity_id}", response_model=YourSchemaResponse)
async def get_entity(entity_id: str):
    """Get an entity by ID."""
    # Implementation here
    pass

@router.put("/{entity_id}", response_model=YourSchemaResponse)
async def update_entity(entity_id: str, entity_data: YourSchemaUpdate):
    """Update an entity."""
    # Implementation here
    pass

@router.delete("/{entity_id}")
async def delete_entity(entity_id: str):
    """Delete an entity."""
    # Implementation here
    pass
```

## Best Practices

1. **Inheritance**: Use inheritance to avoid duplicating fields between schemas
2. **Field Validation**: Use Pydantic validators for complex validation logic
3. **Documentation**: Provide clear descriptions for all fields
4. **Optional Fields**: Make fields optional in update schemas
5. **UUID Handling**: Properly handle UUID serialization in response schemas
6. **Consistent Naming**: Use consistent naming conventions (Create, Update, Response, ListResponse)
7. **Type Hints**: Use proper type hints for all fields
8. **Default Values**: Provide sensible default values where appropriate
9. **Constraints**: Use Pydantic Field constraints (min_length, max_length, etc.) for validation
10. **Config**: Use the Config class for model configuration when needed

## Example Schema

Here's a complete example of a simple schema for a task management system:

```python
from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
import uuid

class TaskBase(BaseModel):
    """Base task schema with common fields."""
    
    title: str = Field(..., min_length=1, max_length=200, description="Task title")
    description: Optional[str] = Field(None, max_length=1000, description="Task description")
    status: str = Field(..., description="Task status (todo, in_progress, done)")
    priority: int = Field(1, ge=1, le=5, description="Task priority (1-5)")
    
    @validator('status')
    def validate_status(cls, v):
        """Validate status values."""
        allowed_statuses = ['todo', 'in_progress', 'done']
        if v not in allowed_statuses:
            raise ValueError(f'Status must be one of {allowed_statuses}')
        return v

class TaskCreate(TaskBase):
    """Schema for creating a new task."""
    
    assignee_id: Optional[str] = Field(None, description="ID of the assignee")

class TaskUpdate(BaseModel):
    """Schema for updating an existing task."""
    
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    status: Optional[str] = None
    priority: Optional[int] = Field(None, ge=1, le=5)
    assignee_id: Optional[str] = None

class TaskResponse(TaskBase):
    """Schema for task response."""
    
    id: Union[str, uuid.UUID]
    assignee_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    owner_id: str
    
    class Config:
        from_attributes = True
        json_encoders = {
            uuid.UUID: str
        }
    
    def model_dump(self, **kwargs):
        """Override to ensure UUID is always converted to string."""
        data = super().model_dump(**kwargs)
        if isinstance(data.get('id'), uuid.UUID):
            data['id'] = str(data['id'])
        return data

class TaskListResponse(BaseModel):
    """Schema for list of tasks response."""
    
    tasks: List[TaskResponse]
    total: int = Field(..., description="Total number of tasks")
    page: int = Field(1, description="Current page number")
    page_size: int = Field(20, description="Number of tasks per page")