Of course! Here is a structured summary of your current requirements, which can be used for subsequent development, communication, or documentation:

---

# 🎯 Project Name: Multi-User, Multi-Session, Multi-Role Xapa AI

---

## 🧠 Project Goal

Build a **Python-based** AI Agent system that supports the following core capabilities:

-   Concurrent use by multiple users.
-   Each user can open multiple sessions.
-   Each session can be bound to a predefined AI role.
-   Support for WebSocket real-time communication.
-   Sessions can be named for easy user management and switching.
-   Context isolation and persistent data storage.
-   Use of GPT series models (e.g., GPT-4).

---

## ✅ Functional Requirements

| Category | Feature | Description |
|---|---|---|
| User Management | Registration | Provide a registration interface. |
| | Login | JWT authentication to generate a token. |
| Session Management | Create Session | Users can create new sessions and name them. |
| | Switch Session | Users can switch between different sessions. |
| | Resume Session | Support for resuming historical sessions on any device. |
| | Delete Session | Users can delete specified sessions. |
| Role System | Preset Roles | Such as "Teacher," "Programmer," etc., not user-customizable. |
| | Role Configuration | Includes prompts, tool permissions, knowledge base sources, etc. |
| Tool Invocation | Role-Specific Tools | Each role enables a different set of tools (e.g., math calculations, code execution). |
| Real-time Communication | WebSocket Support | Supports real-time chat interaction, suitable for web or other clients. |
| Data Persistence | Context Caching | Use Redis to cache the context of each session. |
| | Database Storage | PostgreSQL to store user, session, and message records. |
| Security | JWT Authentication | All API requests must carry a valid token. |
| Deployment | Docker Compose One-Click Deployment | Quickly start the database, Redis, and backend services. |

---

## 🛠️ Technology Stack Requirements

| Category | Technology/Framework |
|---|---|
| Backend Language | Python 3.10+ |
| Web Framework | FastAPI |
| Large Model | OpenAI GPT-4 |
| Agent Construction | LangChain |
| Database | PostgreSQL |
| Caching | Redis |
| User Authentication | JWT + OAuth2PasswordBearer |
| Tool Modules | Custom tools (e.g., `math_solver`, `code_executor`) |
| Logging | logging / custom logger |
| Configuration Management | Pydantic Settings |
| Deployment Method | Docker + Docker Compose |

---

## 📦 Suggested Project Structure

```
./
│
├── app/
│   ├── main.py                 # Entry point
│   ├── api/
│   │   ├── auth.py             # Login/registration interfaces
│   │   ├── chat.py             # WebSocket real-time chat interface
│   │   └── session.py          # Session management interface
│   ├── agent/
│   │   ├── factory.py          # Create Agent based on role
│   │   ├── role_loader.py      # Load role configurations
│   │   └── tools/              # Tool modules
│   ├── session/
│   │   ├── manager.py          # Session lifecycle management
│   │   └── storage.py          # Redis context storage
│   ├── models/                 # SQLAlchemy data models
│   │   ├── user.py
│   │   ├── session.py
│   │   ├── message.py
│   ├── core/
│   │   └── auth.py             # JWT validation logic
│   └── utils/
│       └── logger.py           # Logging utility
│
├── config/
│   └── settings.py             # Configuration file (OpenAI Key, DB, Redis, etc.)
├── roles/
│   └── default_roles.yaml      # Default role configuration file
├── migrations/                 # Alembic database migration scripts
├── tests/                      # Unit test directory
├── .env                        # Environment variable file
├── docker-compose.yml          # One-click deployment configuration
├── requirements.txt            # Python dependencies
└── README.md                   # Project documentation
```

---

## 🧱 Key Module Descriptions

### 1. User Authentication Module
-   Provide `/register` and `/login` interfaces.
-   Use JWT to generate access tokens.
-   Each request must include `Authorization: Bearer <token>`.

### 2. Session Management Module
-   Sessions can be named (e.g., "Math Homework," "Programming Questions").
-   Each session is bound to a role.
-   Support for creating, resuming, and deleting sessions.

### 3. Role System
-   Define roles using a YAML file.
-   Each role includes:
    -   Name, description
    -   Prompt template
    -   List of tool permissions
    -   Knowledge base reference (optional).

### 4. WebSocket Real-time Chat
-   Interface path: `/ws/chat`
-   Parameters: `session_key`, `role_name`, `token`
-   Bidirectional messaging, with support for streaming output (e.g., for gradual responses from large models).

### 5. Database Integration
-   PostgreSQL stores:
    -   User information
    -   Session information
    -   Conversation records
-   Use Alembic for database version management.

### 6. Redis Context Caching
-   The context of each session is stored in Redis.
-   Supports fast loading and updating of context state.

### 7. Tool Modules
-   Each role is bound to a set of tools.
-   Example tools:
    -   `math_solver`: A tool for solving math problems.
    -   `code_executor`: A Python code executor.

---

## 🧪 API Design Samples

### User Registration
```bash
POST /register
{
  "username": "testuser",
  "password": "testpass"
}
```

### User Login
```bash
POST /login
{
  "username": "testuser",
  "password": "testpass"
}
→ Returns token
```

### WebSocket Chat
```bash
WS /ws/chat?session_key=abc123&role_name=teacher
Headers:
  Authorization: Bearer <your_token>
```

---

## 🐳 Docker Deployment Configuration (Simplified)

```yaml
version: '3.8'

services:
  app:
    build: .
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************/mydb
      - REDIS_URL=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=mydb
    ports:
      - "5432:5432"

  redis:
    image: redis:latest
    ports:
      - "6379:6379"
```

---

## 📝 Additional Notes

-   No front-end interface is required.
-   Users are not allowed to upload or customize roles.
-   All roles are predefined by the system.
-   The session naming feature must be implemented.
-   WebSocket support is a key requirement.

---