# Role Switching Feature

## Overview

The role switching feature allows users to dynamically change AI agent roles during conversation sessions without losing chat history. When a role is switched, the system prompt, tools, and configuration are updated while preserving the conversation context.

## Architecture

### Components

1. **Session Manager** (`app/session/manager.py`)
   - Handles role switching at the database level
   - Updates session metadata in both PostgreSQL and Redis

2. **Agent Factory** (`app/agent/factory.py`)
   - Manages agent lifecycle and recreation when roles change
   - Automatically detects role changes and creates new agents

3. **Chat APIs** (`app/api/chat.py`, `app/api/websocket.py`)
   - REST and WebSocket endpoints supporting role switching
   - Real-time notifications for WebSocket clients

4. **Frontend UI** (`templates/test/agent.html`)
   - Interactive role switcher dropdown
   - Real-time role change notifications

### Data Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant SessionManager
    participant AgentFactory
    participant Database
    participant Redis

    Client->>API: Request role switch
    API->>SessionManager: switch_session_role()
    SessionManager->>Database: Update session.role_name
    SessionManager->>Redis: Update session metadata
    API->>AgentFactory: remove_agent()
    API->>AgentFactory: create_agent(new_role)
    AgentFactory->>API: New agent with updated config
    API->>Client: Role switched notification
```

## Implementation Details

### Database Schema

The `sessions` table stores the current role for each session:

```sql
CREATE TABLE sessions (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    session_key VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    role_name VARCHAR(100) NOT NULL,  -- Current active role
    context JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Redis Metadata

Session metadata in Redis is updated with role change information:

```json
{
    "role_name": "new_role",
    "previous_role": "old_role", 
    "role_switched_at": "2025-07-23T21:30:00.000Z",
    "last_accessed": "2025-07-23T21:30:00.000Z"
}
```

## API Usage

### REST API

#### Endpoint: `POST /api/v1/chat/`

**Switch role while sending message:**
```json
{
    "content": "What is your name now?",
    "session_key": "sess_abc123",
    "role_name": "assistant"
}
```

**Response includes current role:**
```json
{
    "content": "I'm Assistant. How can I help?",
    "session_key": "sess_abc123", 
    "role_name": "assistant",
    "message_id": "msg_def456",
    "metadata": {
        "role": "assistant",
        "model": "gpt-4.1",
        "provider": "azure_openai"
    }
}
```

### WebSocket API

#### Endpoint: `ws://localhost:8000/ws/chat`

**Dedicated role switch message:**
```json
{
    "type": "role.switch",
    "role_name": "assistant"
}
```

**Chat message with role switch:**
```json
{
    "type": "chat.message",
    "content": "Hello with new role",
    "role_name": "1001"
}
```

**Role switched notification:**
```json
{
    "type": "role.switched",
    "session_key": "sess_abc123",
    "metadata": {
        "previous_role": "assistant",
        "new_role": "1001", 
        "switched_at": "2025-07-23T21:30:00.000Z"
    }
}
```

## Frontend Integration

### Role Switcher UI

The test page includes a role switcher dropdown that:

- Appears when a session is active
- Excludes the current role from options
- Shows loading states during switching
- Updates automatically on role changes

### JavaScript Implementation

```javascript
// Switch role via WebSocket
async function switchRole() {
    const newRoleName = document.getElementById('currentRoleSwitch').value;
    
    const switchMessage = {
        type: 'role.switch',
        role_name: newRoleName
    };
    
    websocket.send(JSON.stringify(switchMessage));
}

// Handle role switched event
case 'role.switched':
    addMessage('system', `Role switched to ${data.metadata.new_role}`);
    updateRoleSwitcher();
    break;
```

## Configuration

### Role Definitions

Roles are defined in the database with:
- **System Prompt**: Defines AI behavior and personality
- **Tools**: Available functions the AI can call
- **Config**: LLM parameters (temperature, max_tokens, etc.)

Example role configuration:
```json
{
    "name": "assistant",
    "display_name": "AI Assistant", 
    "description": "General purpose AI assistant",
    "system_prompt": "You are a helpful AI assistant...",
    "tools": ["web_search", "calculator"],
    "config": {
        "temperature": 0.7,
        "max_tokens": 1000
    }
}
```

## Error Handling

### Validation

- **Role Existence**: Validates role exists before switching
- **Session Ownership**: Ensures user owns the session
- **Same Role Check**: Prevents unnecessary switches to current role

### Error Responses

**Invalid role:**
```json
{
    "type": "error",
    "metadata": {
        "error": "Role 'invalid_role' does not exist",
        "error_type": "validation_error"
    }
}
```

**Session not found:**
```json
{
    "detail": "Session not found"
}
```

## Performance Considerations

### Agent Caching

- **REST API**: Creates fresh agent per request (stateless)
- **WebSocket**: Maintains agent cache, recreated on role switch
- **Memory Management**: Old agents removed to prevent memory leaks

### Database Optimization

- **Indexed Queries**: Session lookups by `session_key` and `user_id`
- **Connection Pooling**: Efficient database connection management
- **Redis Caching**: Fast session metadata access

## Security

### Access Control

- **User Authentication**: JWT token validation for all requests
- **Session Ownership**: Users can only switch roles in their own sessions
- **Role Permissions**: Future enhancement for role-based access control

### Input Validation

- **Role Name Sanitization**: Prevents injection attacks
- **Message Content Filtering**: Standard input validation
- **Rate Limiting**: Prevents abuse of role switching

## Testing

### Unit Tests

```python
async def test_role_switching():
    # Test role switch in existing session
    response = await client.post("/chat/", json={
        "content": "Hello",
        "session_key": "test_session",
        "role_name": "assistant"
    })
    assert response.json()["role_name"] == "assistant"
```

### Integration Tests

```python
async def test_websocket_role_switch():
    async with websocket_connect("/ws/chat") as websocket:
        # Send role switch message
        await websocket.send_json({
            "type": "role.switch", 
            "role_name": "1001"
        })
        
        # Verify role switched event
        message = await websocket.receive_json()
        assert message["type"] == "role.switched"
```

## Future Enhancements

### Planned Features

1. **Role History**: Track role changes over time
2. **Role Permissions**: Restrict roles based on user access level
3. **Custom Roles**: Allow users to create custom roles
4. **Role Templates**: Pre-configured role templates for common use cases
5. **A/B Testing**: Compare different roles for the same conversation

### Technical Improvements

1. **Batch Operations**: Switch multiple sessions to same role
2. **Background Processing**: Async role switching for large sessions
3. **Metrics**: Track role switching patterns and usage
4. **Rollback**: Ability to revert to previous role

## Troubleshooting

### Common Issues

**Role switch not working:**
- Check if role exists in database
- Verify user has access to session
- Ensure WebSocket connection is active

**System prompt not updating:**
- Confirm agent is recreated after role switch
- Check role configuration in database
- Verify agent factory is getting fresh agent

**Performance issues:**
- Monitor agent creation/destruction cycles
- Check database query performance
- Review Redis connection health

### Debug Commands

```bash
# Check available roles
curl -H "Authorization: Bearer TOKEN" \
  http://localhost:8000/api/v1/roles/list

# Get session info
curl -H "Authorization: Bearer TOKEN" \
  http://localhost:8000/api/v1/sessions/sess_abc123

# Check active WebSocket connections
curl http://localhost:8000/ws/connections
```

## Conclusion

The role switching feature provides a seamless way to change AI behavior during conversations while maintaining context. The implementation spans multiple components and provides both REST and WebSocket interfaces for maximum flexibility.

The feature is designed for scalability, security, and user experience, with comprehensive error handling and real-time updates for WebSocket clients.