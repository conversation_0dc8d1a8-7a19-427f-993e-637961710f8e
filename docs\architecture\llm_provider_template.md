# LLM Provider Implementation Template

This document provides a template for implementing new LLM providers in the Xapa AI system.

## Overview

LLM providers in the Xapa AI system follow a standardized interface defined in `app/llm/base.py`. Each provider must implement the `LLMProvider` abstract base class.

## Template Structure

```python
from typing import List, Dict, Any, AsyncIterator, Optional
from app.llm.base import LLMProvider, ChatMessage, LLMResponse
from app.core.exceptions import LLMError
import logging

logger = logging.getLogger(__name__)

class YourLLMProvider(LLMProvider):
    """Your LLM provider implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the LLM provider.
        
        Args:
            config: Configuration dictionary containing provider settings
        """
        super().__init__(config)
        # Initialize your provider with configuration
        self.api_key = config.get("api_key")
        self.model = config.get("model")
        self.endpoint = config.get("endpoint")
        # Add other provider-specific initialization
        
    async def generate_response(
        self,
        messages: List[ChatMessage],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> LLMResponse:
        """
        Generate a response from the LLM.
        
        Args:
            messages: List of chat messages
            tools: Optional list of tool schemas
            **kwargs: Additional parameters (temperature, max_tokens, etc.)
            
        Returns:
            LLMResponse: Generated response
            
        Raises:
            LLMError: If response generation fails
        """
        try:
            # Implement your response generation logic here
            # This should handle:
            # 1. Converting ChatMessage objects to your provider's format
            # 2. Adding tool/function calling support if applicable
            # 3. Applying configuration parameters (temperature, max_tokens, etc.)
            # 4. Making the API call to your provider
            # 5. Converting the response to an LLMResponse object
            
            # Example placeholder implementation:
            response_data = {
                "content": "Generated response",
                "metadata": {
                    "model": self.model,
                    "provider": "your_provider"
                }
            }
            
            return LLMResponse(**response_data)
            
        except Exception as e:
            logger.error(f"Failed to generate response: {str(e)}")
            raise LLMError(f"Your provider error: {str(e)}")
    
    async def generate_stream(
        self,
        messages: List[ChatMessage],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> AsyncIterator[LLMResponse]:
        """
        Generate a streaming response from the LLM.
        
        Args:
            messages: List of chat messages
            tools: Optional list of tool schemas
            **kwargs: Additional parameters (temperature, max_tokens, etc.)
            
        Yields:
            LLMResponse: Streaming response chunks
            
        Raises:
            LLMError: If response generation fails
        """
        try:
            # Implement your streaming response logic here
            # This should yield LLMResponse objects as they become available
            
            # Example placeholder implementation:
            yield LLMResponse(
                content="Streaming ",
                is_complete=False
            )
            
            yield LLMResponse(
                content="response",
                is_complete=True
            )
            
        except Exception as e:
            logger.error(f"Failed to generate streaming response: {str(e)}")
            raise LLMError(f"Your provider error: {str(e)}")
    
    async def validate_connection(self) -> bool:
        """
        Validate the connection to the LLM provider.
        
        Returns:
            bool: True if connection is valid, False otherwise
        """
        try:
            # Implement connection validation logic
            # This might involve making a simple API call to verify credentials
            return True
        except Exception as e:
            logger.error(f"Connection validation failed: {str(e)}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the current model.
        
        Returns:
            Dict[str, Any]: Model information
        """
        return {
            "model": self.model,
            "provider": "your_provider",
            "capabilities": ["text_generation", "streaming"]
        }

# Factory function for creating provider instances
def create_your_provider(config: Dict[str, Any]) -> YourLLMProvider:
    """
    Create an instance of your LLM provider.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        YourLLMProvider: Provider instance
    """
    return YourLLMProvider(config)
```

## Registration

To register your provider with the factory, add it to `app/llm/provider_factory.py`:

```python
# In provider_factory.py
from your_provider_module import create_your_provider

# Add to the factory registration
LLMProviderFactory.register_provider("your_provider", create_your_provider)
```

And update the configuration in `app/core/config.py`:

```python
# In config.py
class Settings(BaseSettings):
    # ... other settings ...
    
    # Your provider settings
    your_provider_api_key: Optional[str] = Field(default=None, env="YOUR_PROVIDER_API_KEY")
    your_provider_model: Optional[str] = Field(default=None, env="YOUR_PROVIDER_MODEL")
    your_provider_endpoint: Optional[str] = Field(default=None, env="YOUR_PROVIDER_ENDPOINT")
```

## Configuration

Add the following environment variables to `.env.example`:

```env
# Your Provider Configuration
YOUR_PROVIDER_API_KEY=your_api_key_here
YOUR_PROVIDER_MODEL=your_model_name
YOUR_PROVIDER_ENDPOINT=your_endpoint_url
```

## Usage

Once implemented and registered, your provider can be used by setting the `LLM_PROVIDER` environment variable to `"your_provider"`.

## Best Practices

1. **Error Handling**: Always wrap API calls in try/except blocks and raise appropriate `LLMError` exceptions
2. **Logging**: Use the provided logger to log important events and errors
3. **Configuration**: Use the configuration system to manage provider-specific settings
4. **Validation**: Implement connection validation to ensure the provider is properly configured
5. **Streaming**: Implement streaming support if your provider supports it
6. **Tool Calling**: Implement tool/function calling support if your provider supports it
7. **Rate Limiting**: Consider implementing rate limiting if your provider has usage limits
8. **Retries**: Implement retry logic for transient failures