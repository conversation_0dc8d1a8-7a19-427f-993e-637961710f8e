from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
import uuid


class ChatMessage(BaseModel):
    """Schema for chat message."""
    content: str = Field(..., min_length=1, max_length=4000, description="Message content")
    session_key: Optional[str] = Field(None, description="Session key (optional for new sessions)")
    role_name: Optional[str] = Field(None, description="AI role (required for new sessions or role switching)")


class ChatResponse(BaseModel):
    """Schema for chat response."""
    content: str
    session_key: str
    role_name: str
    message_id: str
    metadata: Dict[str, Any]
    created_at: datetime
    prompt_tokens: Optional[int] = None
    completion_tokens: Optional[int] = None
    total_tokens: Optional[int] = None


class MessageResponse(BaseModel):
    """Schema for message response."""
    id: Union[str, uuid.UUID]
    session_id: Union[str, uuid.UUID]
    role: str  # 'user', 'assistant', 'system'
    content: str
    metadata: Dict[str, Any]
    sequence_number: int
    created_at: datetime
    prompt_tokens: Optional[int] = None
    completion_tokens: Optional[int] = None
    total_tokens: Optional[int] = None

    class Config:
        from_attributes = True
        # Map the database field name to the schema field name
        populate_by_name = True
        # Automatically convert UUID to string
        json_encoders = {
            uuid.UUID: str
        }
    
    @classmethod
    def from_orm(cls, obj):
        """Custom from_orm to handle field name mapping."""
        return cls(
            id=obj.id,
            session_id=obj.session_id,
            role=obj.role,
            content=obj.content,
            metadata=obj.message_metadata or {},
            sequence_number=obj.sequence_number,
            created_at=obj.created_at,
            prompt_tokens=obj.prompt_tokens,
            completion_tokens=obj.completion_tokens,
            total_tokens=obj.total_tokens
        )
        
    def model_dump(self, **kwargs):
        """Override to ensure UUIDs are always converted to strings."""
        data = super().model_dump(**kwargs)
        if isinstance(data.get('id'), uuid.UUID):
            data['id'] = str(data['id'])
        if isinstance(data.get('session_id'), uuid.UUID):
            data['session_id'] = str(data['session_id'])
        return data


class MessageHistoryResponse(BaseModel):
    """Schema for message history response."""
    messages: List[MessageResponse]
    session_key: str
    total: int
    page: int
    page_size: int


class StreamingMessage(BaseModel):
    """Schema for streaming message events."""
    type: str  # 'chat.message.start', 'chat.message.chunk', 'chat.message.end', 'role.switched'
    message_id: Optional[str] = None
    session_key: Optional[str] = None
    content: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class RoleResponse(BaseModel):
    """Schema for role information."""
    name: str
    display_name: str
    description: str
    tools: List[str]
    is_active: bool


class RoleListResponse(BaseModel):
    """Schema for roles list response."""
    roles: List[RoleResponse]