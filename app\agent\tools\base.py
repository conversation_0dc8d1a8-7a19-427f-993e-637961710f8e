from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Type
from pydantic import BaseModel, Field, validator
import logging

logger = logging.getLogger(__name__)


class ToolSchema(BaseModel):
    """Schema for tool definition."""
    name: str = Field(..., description="Tool name identifier")
    description: str = Field(..., description="Tool description")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Tool parameters schema")
    required: List[str] = Field(default_factory=list, description="Required parameter names")
    
    @validator('name')
    def validate_name(cls, v):
        if not v or not v.replace('_', '').isalnum():
            raise ValueError("Tool name must be alphanumeric with underscores")
        return v


class ToolResult(BaseModel):
    """Result from tool execution."""
    success: bool = Field(..., description="Whether the tool execution was successful")
    result: Any = Field(None, description="Tool execution result")
    error: Optional[str] = Field(None, description="Error message if execution failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    @classmethod
    def success_result(cls, result: Any, metadata: Optional[Dict[str, Any]] = None) -> 'ToolResult':
        """Create a successful tool result."""
        return cls(success=True, result=result, metadata=metadata or {})
    
    @classmethod
    def error_result(cls, error: str, metadata: Optional[Dict[str, Any]] = None) -> 'ToolResult':
        """Create an error tool result."""
        return cls(success=False, error=error, metadata=metadata or {})


class BaseTool(ABC):
    """Abstract base class for AI agent tools."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Tool name identifier."""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Tool description."""
        pass
    
    @abstractmethod
    def get_schema(self) -> ToolSchema:
        """Get tool schema for LLM function calling."""
        pass
    
    @abstractmethod
    async def execute(self, user_context: Optional[Dict[str, Any]] = None, **kwargs) -> ToolResult:
        """
        Execute the tool with given parameters and user context.
        
        Args:
            user_context: Optional user context containing user_id, tenant_id, client_id, session_key
            **kwargs: Tool parameters
            
        Returns:
            ToolResult: Execution result
        """
        pass
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> tuple[bool, Optional[str]]:
        """
        Validate tool parameters against schema.
        
        Args:
            parameters: Parameters to validate
            
        Returns:
            tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        try:
            schema = self.get_schema()
            
            # Check required parameters
            missing_params = [param for param in schema.required if param not in parameters]
            if missing_params:
                return False, f"Missing required parameters: {', '.join(missing_params)}"
            
            # Validate parameter types if schema defines them
            if hasattr(schema, 'parameters') and 'properties' in schema.parameters:
                for param_name, param_value in parameters.items():
                    if param_name in schema.parameters['properties']:
                        param_schema = schema.parameters['properties'][param_name]
                        if not self._validate_parameter_type(param_value, param_schema):
                            return False, f"Invalid type for parameter '{param_name}'"
            
            return True, None
        except Exception as e:
            logger.error(f"Parameter validation error for {self.name}: {str(e)}")
            return False, f"Validation error: {str(e)}"
    
    def _validate_parameter_type(self, value: Any, schema: Dict[str, Any]) -> bool:
        """
        Validate a parameter value against its schema.
        
        Args:
            value: Parameter value to validate
            schema: Parameter schema definition
            
        Returns:
            bool: True if valid
        """
        param_type = schema.get('type')
        if not param_type:
            return True
        
        type_map = {
            'string': str,
            'integer': int,
            'number': (int, float),
            'boolean': bool,
            'array': list,
            'object': dict
        }
        
        expected_type = type_map.get(param_type)
        if expected_type and not isinstance(value, expected_type):
            return False
        
        return True


class ToolRegistry:
    """Registry for managing available tools."""
    
    def __init__(self):
        self._tools: Dict[str, BaseTool] = {}
        self._tool_types: Dict[str, Type[BaseTool]] = {}
    
    def register_tool(self, tool: BaseTool) -> bool:
        """Register a tool.
        
        Args:
            tool: Tool instance to register
            
        Returns:
            bool: True if registration successful
        """
        try:
            if not isinstance(tool, BaseTool):
                logger.error(f"Cannot register non-BaseTool: {type(tool)}")
                return False
            
            if tool.name in self._tools:
                logger.warning(f"Tool '{tool.name}' already registered, overwriting")
            
            self._tools[tool.name] = tool
            self._tool_types[tool.name] = type(tool)
            logger.info(f"Successfully registered tool: {tool.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to register tool {getattr(tool, 'name', 'unknown')}: {str(e)}")
            return False
    
    def unregister_tool(self, name: str) -> bool:
        """Unregister a tool by name.
        
        Args:
            name: Tool name to unregister
            
        Returns:
            bool: True if unregistration successful
        """
        if name in self._tools:
            del self._tools[name]
            self._tool_types.pop(name, None)
            logger.info(f"Unregistered tool: {name}")
            return True
        return False
    
    def get_tool(self, name: str) -> Optional[BaseTool]:
        """Get a tool by name.
        
        Args:
            name: Tool name
            
        Returns:
            Optional[BaseTool]: Tool instance if found
        """
        return self._tools.get(name)
    
    def list_tools(self) -> List[str]:
        """List all registered tool names.
        
        Returns:
            List[str]: List of tool names
        """
        return list(self._tools.keys())
    
    def get_tools_by_names(self, tool_names: List[str]) -> tuple[List[BaseTool], List[str]]:
        """Get tools by their names.
        
        Args:
            tool_names: List of tool names
            
        Returns:
            tuple[List[BaseTool], List[str]]: (found_tools, missing_tools)
        """
        found_tools = []
        missing_tools = []
        
        for name in tool_names:
            tool = self.get_tool(name)
            if tool:
                found_tools.append(tool)
            else:
                missing_tools.append(name)
        
        if missing_tools:
            logger.warning(f"Missing tools: {missing_tools}")
        
        return found_tools, missing_tools
    
    def get_tool_schemas(self, tool_names: List[str]) -> tuple[List[ToolSchema], List[str]]:
        """Get schemas for specified tools.
        
        Args:
            tool_names: List of tool names
            
        Returns:
            tuple[List[ToolSchema], List[str]]: (schemas, missing_tools)
        """
        schemas = []
        missing_tools = []
        
        for name in tool_names:
            tool = self.get_tool(name)
            if tool:
                try:
                    schemas.append(tool.get_schema())
                except Exception as e:
                    logger.error(f"Failed to get schema for tool {name}: {str(e)}")
                    missing_tools.append(name)
            else:
                missing_tools.append(name)
        
        return schemas, missing_tools
    
    def validate_all_tools(self) -> Dict[str, bool]:
        """Validate all registered tools.
        
        Returns:
            Dict[str, bool]: Tool name to validation status mapping
        """
        validation_results = {}
        
        for name, tool in self._tools.items():
            try:
                schema = tool.get_schema()
                validation_results[name] = bool(schema.name and schema.description)
            except Exception as e:
                logger.error(f"Tool {name} validation failed: {str(e)}")
                validation_results[name] = False
        
        return validation_results
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """Get registry statistics.
        
        Returns:
            Dict[str, Any]: Registry statistics
        """
        return {
            'total_tools': len(self._tools),
            'tool_names': list(self._tools.keys()),
            'tool_types': {name: cls.__name__ for name, cls in self._tool_types.items()}
        }


# Global tool registry instance
tool_registry = ToolRegistry()