# Architecture Documentation

This directory contains documentation about the system architecture and implementation templates for various components.

## Contents

- [Folder Structure](folder_structure.md) - Detailed folder architecture documentation
- [LLM Provider Template](llm_provider_template.md) - Template for implementing new LLM providers
- [Tool Template](tool_template.md) - Template for implementing new tools
- [Schema Template](schema_template.md) - Template for implementing new schemas
- [Embedding Provider Template](embedding_provider_template.md) - Template for implementing new embedding providers
- [API Endpoint Template](api_endpoint_template.md) - Template for implementing new API endpoints

## Purpose

This documentation is intended to help developers understand the system architecture and implement new components following established patterns and best practices.

## Using the Templates

Each template provides:
- A standardized structure for implementing new components
- Best practices and guidelines
- Example implementations
- Integration instructions

Follow the templates when adding new:
- LLM providers
- Tools
- Schemas
- Embedding providers
- API endpoints