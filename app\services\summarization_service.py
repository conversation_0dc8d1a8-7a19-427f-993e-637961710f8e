"""
Simple summarization service for generating session names.
"""
import re
from typing import Optional
import logging

from app.llm.base import ChatMessage
from app.llm.provider_factory import create_llm_provider

logger = logging.getLogger(__name__)


class SummarizationService:
    """Simple service for generating session names using LLM."""
    
    @staticmethod
    async def summarize_for_session_name(content: str, max_length: int = 50) -> Optional[str]:
        """Generate a session name from user message. Returns None if message is not suitable."""
        if not content:
            return None
            
        # Clean up content
        clean_content = re.sub(r'\s+', ' ', content.strip())
        
        # Check if message is suitable for session naming
        if len(clean_content) < 3 or len(clean_content) > 200:
            return None
        
        # If short enough, use as-is
        if len(clean_content) <= max_length:
            return clean_content
            
        # Try LLM summarization
        try:
            llm = create_llm_provider(config={"temperature": 0.1, "max_tokens": 30})
            
            prompt = f"Create a {max_length}-character session title for: {clean_content}"
            messages = [ChatMessage(role="user", content=prompt)]
            
            response = await llm.generate_response(messages)
            if response.content:
                title = response.content.strip().strip('"\'')
                if len(title) > max_length:
                    title = title[:max_length-3] + "..."
                return title
                
        except Exception as e:
            logger.warning(f"LLM failed: {e}")
            
        # Fallback: simple truncation
        words = clean_content.split()
        result = []
        length = 0
        
        for word in words:
            if length + len(word) + 1 > max_length - 3:
                break
            result.append(word)
            length += len(word) + 1
            
        return ' '.join(result) + "..." if result else clean_content[:max_length-3] + "..."