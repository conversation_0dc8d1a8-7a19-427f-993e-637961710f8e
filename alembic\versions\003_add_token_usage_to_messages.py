"""Add token usage tracking to messages table

Revision ID: 003_add_token_usage
Revises: 002_add_image_url
Create Date: 2025-07-28 14:52:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '003_add_token_usage'
down_revision = '002_add_image_url'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add token usage columns to messages table
    op.add_column('messages', sa.Column('prompt_tokens', sa.Integer(), nullable=True))
    op.add_column('messages', sa.Column('completion_tokens', sa.Integer(), nullable=True))
    op.add_column('messages', sa.Column('total_tokens', sa.Integer(), nullable=True))


def downgrade() -> None:
    # Remove token usage columns from messages table
    op.drop_column('messages', 'total_tokens')
    op.drop_column('messages', 'completion_tokens')
    op.drop_column('messages', 'prompt_tokens')