import uuid
from typing import Optional
from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions
from datetime import datetime, timedelta
import logging

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class AzureStorageService:
    """Service for handling Azure Blob Storage operations for role images."""
    
    def __init__(self):
        """
        Initialize the Azure Storage Service.
        
        Raises:
            ValueError: If Azure Storage credentials are not configured.
        """
        if not settings.azure_storage_account_name or not settings.azure_storage_account_key:
            raise ValueError("Azure Storage credentials not configured")
        
        self.account_name = settings.azure_storage_account_name
        self.account_key = settings.azure_storage_account_key
        self.container_name = settings.azure_storage_container_name
        
        self.blob_service_client = BlobServiceClient(
            account_url=f"https://{self.account_name}.blob.core.windows.net",
            credential=self.account_key
        )
        
        # Ensure container exists
        self._ensure_container_exists()
    
    def _ensure_container_exists(self):
        """Ensure the blob container exists."""
        try:
            container_client = self.blob_service_client.get_container_client(self.container_name)
            container_client.create_container()
            logger.info(f"Created container: {self.container_name}")
        except Exception as e:
            if "ContainerAlreadyExists" in str(e):
                logger.debug(f"Container {self.container_name} already exists")
            else:
                logger.error(f"Error creating container: {e}")
                raise
    
    def upload_image(self, image_data: bytes, blob_name: str, content_type: str = None) -> str:
        """
        Upload image to Azure Blob Storage and return the blob URL.
        
        Args:
            image_data: Raw image bytes
            blob_name: The full name of the blob
            content_type: MIME type of the image
            
        Returns:
            str: The blob URL
        """
        try:
            # Upload to blob storage
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container_name, 
                blob=blob_name
            )
            
            blob_client.upload_blob(
                image_data, 
                content_type=content_type,
                overwrite=True
            )
            
            # Return blob_url
            blob_url = f"https://{self.account_name}.blob.core.windows.net/{self.container_name}/{blob_name}"
            logger.info(f"Uploaded image: {blob_url}")
            return blob_url
            
        except Exception as e:
            logger.error(f"Error uploading image: {e}")
            raise

    def delete_blob(self, blob_url: str) -> bool:
        """
        Delete a blob from storage.
        
        Args:
            blob_url: The blob URL to delete
            
        Returns:
            bool: True if successful
        """
        try:
            # Extract blob name from URL
            blob_name = blob_url.split(f"{self.container_name}/")[-1]
            
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container_name, 
                blob=blob_name
            )
            
            blob_client.delete_blob()
            logger.info(f"Deleted blob: {blob_url}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting blob {blob_url}: {e}")
            return False


# Global service instance
azure_storage_service = None

def get_azure_storage_service() -> Optional[AzureStorageService]:
    """Get Azure Storage Service instance."""
    global azure_storage_service
    
    if azure_storage_service is None:
        try:
            azure_storage_service = AzureStorageService()
        except ValueError as e:
            logger.warning(f"Azure Storage not configured: {e}")
            return None
    
    return azure_storage_service