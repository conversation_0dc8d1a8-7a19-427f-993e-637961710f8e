# API Endpoint Implementation Template

This document provides a template for implementing new API endpoints in the Xapa AI system.

## Overview

API endpoints in the Xapa AI system are implemented using FastAPI and organized by functionality in the `app/api/` directory.

## Template Structure

```python
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from app.models.base import get_db
from app.models.user import User
from app.schemas.your_schema import YourEntityCreate, YourEntityUpdate, YourEntityResponse
from app.core.dependencies import get_current_active_user
from app.core.exceptions import NotFoundError, ConflictError, ValidationError
# Import your service if you have one
# from app.services.your_service import YourService

logger = logging.getLogger(__name__)

router = APIRouter()

# Helper functions (if needed)
def get_your_entity_by_id(db: Session, entity_id: str) -> Optional[YourEntity]:
    """Get an entity by ID."""
    # Implementation here
    pass

def list_your_entities(db: Session, skip: int = 0, limit: int = 100) -> List[YourEntity]:
    """List entities with pagination."""
    # Implementation here
    pass

def create_your_entity_in_db(db: Session, entity_data: YourEntityCreate) -> YourEntity:
    """Create a new entity in the database."""
    # Implementation here
    pass

def update_your_entity_in_db(db: Session, entity_id: str, entity_data: YourEntityUpdate) -> Optional[YourEntity]:
    """Update an existing entity in the database."""
    # Implementation here
    pass

def delete_your_entity_in_db(db: Session, entity_id: str) -> bool:
    """Delete an entity from the database."""
    # Implementation here
    pass

@router.get("/", response_model=List[YourEntityResponse])
async def list_entities(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    List all entities.
    
    Args:
        skip: Number of entities to skip (for pagination)
        limit: Maximum number of entities to return
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[YourEntityResponse]: List of entities
        
    Raises:
        HTTPException: If operation fails
    """
    try:
        entities = list_your_entities(db, skip=skip, limit=limit)
        return [YourEntityResponse.from_orm(entity) for entity in entities]
    except Exception as e:
        logger.error(f"Failed to list entities: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve entities"
        )

@router.get("/{entity_id}", response_model=YourEntityResponse)
async def get_entity(
    entity_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific entity by ID.
    
    Args:
        entity_id: ID of the entity to retrieve
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        YourEntityResponse: Entity details
        
    Raises:
        HTTPException: If entity not found or operation fails
    """
    try:
        entity = get_your_entity_by_id(db, entity_id)
        if not entity:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entity not found"
            )
        return YourEntityResponse.from_orm(entity)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get entity {entity_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve entity"
        )

@router.post("/", response_model=YourEntityResponse, status_code=status.HTTP_201_CREATED)
async def create_entity(
    entity_data: YourEntityCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Create a new entity.
    
    Args:
        entity_data: Entity creation data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        YourEntityResponse: Created entity
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        entity = create_your_entity_in_db(db, entity_data)
        return YourEntityResponse.from_orm(entity)
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to create entity: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create entity"
        )

@router.put("/{entity_id}", response_model=YourEntityResponse)
async def update_entity(
    entity_id: str,
    entity_data: YourEntityUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update an existing entity.
    
    Args:
        entity_id: ID of the entity to update
        entity_data: Entity update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        YourEntityResponse: Updated entity
        
    Raises:
        HTTPException: If entity not found or update fails
    """
    try:
        entity = update_your_entity_in_db(db, entity_id, entity_data)
        if not entity:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entity not found"
            )
        return YourEntityResponse.from_orm(entity)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to update entity {entity_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update entity"
        )

@router.delete("/{entity_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_entity(
    entity_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Delete an entity.
    
    Args:
        entity_id: ID of the entity to delete
        current_user: Current authenticated user
        db: Database session
        
    Raises:
        HTTPException: If entity not found or deletion fails
    """
    try:
        success = delete_your_entity_in_db(db, entity_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entity not found"
            )
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to delete entity {entity_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete entity"
        )
```

## File Location

Create your API endpoint file in the `app/api/` directory:
```
app/api/your_endpoint.py
```

## Registration

Register your router in `app/main.py`:

```python
# In app/main.py
from app.api.your_endpoint import router as your_endpoint_router

# Add to the list of included routers
app.include_router(your_endpoint_router, prefix="/api/v1/your-endpoint", tags=["Your Endpoint"])
```

## Best Practices

1. **Error Handling**: Use appropriate HTTP status codes and consistent error responses
2. **Logging**: Log important events and errors for debugging and monitoring
3. **Dependencies**: Use FastAPI dependencies for authentication, database sessions, etc.
4. **Validation**: Use Pydantic schemas for request/response validation
5. **Documentation**: Provide clear docstrings for all endpoints
6. **Pagination**: Implement pagination for list endpoints
7. **Security**: Ensure proper authentication and authorization for all endpoints
8. **Rate Limiting**: Consider implementing rate limiting for public endpoints
9. **Consistent Naming**: Use consistent naming conventions for endpoints
10. **Response Models**: Always specify response models for better API documentation
11. **Exception Handling**: Catch specific exceptions and convert them to appropriate HTTP responses
12. **Database Transactions**: Use proper database transaction handling

## Example Endpoint

Here's a complete example of a simple task management endpoint:

```python
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
import logging

from app.models.base import get_db
from app.models.user import User
from app.models.task import Task as TaskModel
from app.schemas.task import TaskCreate, TaskUpdate, TaskResponse
from app.core.dependencies import get_current_active_user

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/", response_model=List[TaskResponse])
async def list_tasks(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """List all tasks for the current user."""
    try:
        tasks = db.query(TaskModel).filter(
            TaskModel.owner_id == current_user.id
        ).offset(skip).limit(limit).all()
        return [TaskResponse.from_orm(task) for task in tasks]
    except Exception as e:
        logger.error(f"Failed to list tasks: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tasks"
        )

@router.post("/", response_model=TaskResponse, status_code=status.HTTP_201_CREATED)
async def create_task(
    task_data: TaskCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new task."""
    try:
        db_task = TaskModel(
            title=task_data.title,
            description=task_data.description,
            status=task_data.status,
            priority=task_data.priority,
            owner_id=current_user.id
        )
        db.add(db_task)
        db.commit()
        db.refresh(db_task)
        return TaskResponse.from_orm(db_task)
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to create task: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create task"
        )

@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get a specific task by ID."""
    task = db.query(TaskModel).filter(
        TaskModel.id == task_id,
        TaskModel.owner_id == current_user.id
    ).first()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    return TaskResponse.from_orm(task)

@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: str,
    task_data: TaskUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update an existing task."""
    task = db.query(TaskModel).filter(
        TaskModel.id == task_id,
        TaskModel.owner_id == current_user.id
    ).first()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    # Update only provided fields
    update_data = task_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(task, key, value)
    
    db.commit()
    db.refresh(task)
    return TaskResponse.from_orm(task)

@router.delete("/{task_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_task(
    task_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a task."""
    task = db.query(TaskModel).filter(
        TaskModel.id == task_id,
        TaskModel.owner_id == current_user.id
    ).first()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    db.delete(task)
    db.commit()