# Multi-User, Multi-Session, Multi-Role Xapa AI

A comprehensive AI agent system built with FastAPI that supports multiple users, sessions, and AI roles with real-time communication capabilities.

## 🚀 Features

-   **Multi-User Support**: User registration and authentication with JWT tokens for API access.
-   **Multi-Session Management**: Users can create, name, and manage multiple chat sessions with persistence.
-   **Multi-Role AI System**: Predefined AI roles with specialized capabilities, dynamically loaded from database and YAML.
-   **Real-time Communication**: WebSocket support for streaming responses.
-   **Session Persistence**: Redis caching with PostgreSQL storage for all session and message data.
-   **Role-based Tools**: Each AI role has access to specific tools and capabilities, with dynamic tool validation.
-   **Embedding Support**: Text embeddings, similarity search, and clustering capabilities with multiple provider options.
-   **Secure Authentication**: JWT-based authentication for API access and Auth0 integration for web interface.
-   **Web Interface**: Basic web interface for role management and testing.

## 🏗️ Architecture

### Tech Stack
-   **Backend**: Python 3.11+ with FastAPI
-   **Database**: PostgreSQL for persistent storage
-   **Cache**: Redis for session context caching
-   **AI Models**: OpenAI GPT-4, Azure OpenAI (with abstraction for other providers)
-   **Authentication**: JWT tokens with OAuth2
-   **Deployment**: Docker & Docker Compose

### Project Structure
```
├── app/
│   ├── main.py                 # FastAPI application entry point
│   ├── api/                    # API endpoints
│   │   ├── auth.py             # Authentication endpoints
│   │   ├── chat.py             # Chat API endpoints
│   │   ├── embedding.py        # Embedding API endpoints
│   │   ├── roles.py            # Role management API endpoints
│   │   ├── session.py          # Session management API endpoints
│   │   ├── tools.py            # Tool management API endpoints
│   │   ├── web.py              # Web interface routes
│   │   └── websocket.py        # WebSocket handlers
│   ├── agent/                  # AI agent system
│   │   ├── factory.py          # Agent factory and memory management
│   │   ├── role_loader.py      # Role configuration loader (YAML & DB)
│   │   └── tools/              # AI agent tools (e.g., code_tools, health_tools)
│   ├── core/                   # Core functionality
│   │   ├── config.py           # Configuration management (Pydantic settings)
│   │   ├── security.py         # Security utilities (JWT, password hashing)
│   │   ├── dependencies.py     # FastAPI dependencies
│   │   └── exceptions.py       # Custom exception classes
│   ├── embedding/              # Embedding provider abstraction
│   │   ├── base.py             # Base embedding provider interface
│   │   ├── openai_provider.py  # OpenAI embedding integration
│   │   ├── azure_openai_provider.py # Azure OpenAI embedding integration
│   │   ├── provider_factory.py # Embedding provider factory
│   │   └── service.py          # Embedding service logic
│   ├── llm/                    # LLM provider abstraction
│   │   ├── base.py             # Base LLM provider interface
│   │   ├── openai_provider.py  # OpenAI integration
│   │   ├── azure_openai_provider.py # Azure OpenAI integration
│   │   └── provider_factory.py # Provider factory
│   ├── models/                 # SQLAlchemy ORM models
│   │   ├── base.py             # Database engine and base class
│   │   ├── user.py             # User model
│   │   ├── session.py          # Session model
│   │   ├── message.py          # Message model
│   │   └── role.py             # Role model
│   ├── schemas/                # Pydantic schemas for API validation
│   │   ├── auth.py             # Authentication schemas
│   │   ├── chat.py             # Chat schemas
│   │   ├── embedding.py        # Embedding schemas
│   │   ├── role.py             # Role schemas
│   │   ├── session.py          # Session schemas
│   │   └── tool.py             # Tool schemas
│   ├── services/               # Business logic services (e.g., role_service)
│   │   └── (e.g., role_service.py)
│   ├── session/                # Session management
│   │   ├── manager.py          # Session business logic
│   │   └── storage.py          # Session storage (Redis)
│   └── utils/                  # Utilities
│       └── database.py         # Database utilities
├── config/                     # Nginx configuration files
├── static/                     # Static web assets (CSS, JS)
├── templates/                  # Jinja2 HTML templates
├── docker-compose.yml          # Docker services configuration
├── Dockerfile                  # Docker image configuration
├── requirements.txt            # Python dependencies
└── .env.example                # Environment variables template
```

## 🚀 Quick Start

### Prerequisites
-   Docker and Docker Compose
-   Python 3.11+ (for local development)

### Using Docker (Recommended)

1.  **Clone and setup environment**:
    ```bash
    git clone <repository-url>
    cd ai-agent-system
    cp .env.example .env
    # Edit .env with your OpenAI API key and other settings
    ```

2.  **Start services**:
    ```bash
    docker-compose up -d
    ```

3.  **Access the API**:
    -   API Documentation: http://localhost:8000/docs
    -   Health Check: http://localhost:8000/health

### Local Development

1.  **Install dependencies**:
    ```bash
    pip install -r requirements.txt
    ```

2.  **Setup environment**:
    ```bash
    cp .env.example .env
    # Edit .env with your configuration
    ```

3.  **Start PostgreSQL and Redis** (via Docker):
    ```bash
    docker-compose up -d db redis
    ```

4.  **Run the application**:
    ```bash
    uvicorn app.main:app --reload
    ```

## 📚 API Usage

### Authentication

The system requires authentication for all API endpoints:

**API Authentication**: JWT tokens are required for all API calls. The system supports external JWT token verification (for tokens generated by other apps with the same secret key):
```bash
curl -X GET "http://localhost:8000/api/v1/auth/verify" \
  -H "Authorization: Bearer YOUR_EXTERNAL_JWT_TOKEN"
```

Response:
```json
{
  "id": "user-uuid",
  "username": "user_12345678",
  "email": "<EMAIL>",
  "is_active": true
}
```

### Using the API with Authentication

Include the JWT token in the Authorization header:
```bash
curl -X GET "http://localhost:8000/api/v1/sessions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🤖 AI Roles

The system supports AI roles with specialized capabilities, dynamically loaded from both YAML files and the database:

### Role Management
-   **Database Storage**: All roles are stored in the database for dynamic management and override YAML configurations.
-   **YAML Configuration**: Default roles can be defined in `roles/default_roles.yaml`.
-   **Web Interface**: Create, edit, and manage roles through the web interface (accessible via `/roles`).
-   **API Access**: Full CRUD operations available via REST API (`/api/v1/roles`).
-   **Tool Integration**: Each role can be configured with specific tools and capabilities, with automatic validation and filtering of invalid tools.

### Role Configuration
Roles can be created and managed through:

1.  **Web Interface**: Visit `/roles` to manage roles through the UI
2.  **API Endpoints**: Use the `/api/v1/roles` endpoints for programmatic access
3.  **Database Direct**: Roles are stored in the `roles` table

Example role structure:
```json
{
  "name": "teacher",
  "display_name": "Math Teacher",
  "description": "Expert mathematics educator",
  "system_prompt": "You are an experienced mathematics teacher...",
  "tools": ["math_solver", "knowledge_search"],
  "config": {
    "temperature": 0.7,
    "max_tokens": 2000
  },
  "is_active": true
}
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|---|---|---|
| `DATABASE_URL` | PostgreSQL connection string | Required |
| `REDIS_URL` | Redis connection string | Required |
| `LLM_PROVIDER` | LLM provider to use (`openai`, `azure_openai`) | `azure_openai` |
| `OPENAI_API_KEY` | OpenAI API key | Optional |
| `OPENAI_MODEL` | OpenAI model name | `gpt-4` |
| `AZURE_OPENAI_API_KEY` | Azure OpenAI API key | Optional |
| `AZURE_OPENAI_ENDPOINT` | Azure OpenAI endpoint URL | Optional |
| `AZURE_OPENAI_DEPLOYMENT` | Azure OpenAI deployment name | Optional |
| `AZURE_OPENAI_API_VERSION` | Azure OpenAI API version | `2024-02-15-preview` |
| `SECRET_KEY` | JWT signing secret | Required |
| `MAX_SESSIONS_PER_USER` | Max sessions per user | 10 |
| `JWT_EXPIRE_HOURS` | JWT token expiration | 24 |

### LLM Providers

The system supports multiple LLM providers that can be configured via environment variables:

#### Supported Providers

1.  **OpenAI**
    -   Industry-standard GPT models
    -   Set `LLM_PROVIDER=openai`
    -   Requires `OPENAI_API_KEY`

2.  **Azure OpenAI**
    -   Enterprise-grade with data residency
    -   Set `LLM_PROVIDER=azure_openai`
    -   Requires `AZURE_OPENAI_API_KEY`, `AZURE_OPENAI_ENDPOINT`, and `AZURE_OPENAI_DEPLOYMENT`

#### Provider Configuration Examples

```env
# Using OpenAI
LLM_PROVIDER=openai
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4

# Using Azure OpenAI (default)
LLM_PROVIDER=azure_openai
AZURE_OPENAI_API_KEY=your_azure_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT=your-model-name
```

### Embedding Support

The system includes comprehensive embedding support for text vectorization, similarity search, and clustering:

```env
# Embedding Provider Configuration
EMBEDDING_PROVIDER=azure_openai

# OpenAI Embeddings
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# Azure OpenAI Embeddings
AZURE_OPENAI_EMBEDDING_MODEL=text-embedding-3-small
```

### Session Constraints
-   **Session names**: 3-100 characters, alphanumeric and spaces only
-   **Must be unique per user**
-   **Maximum sessions per user**: Configurable (default: 10)

### Database Schema
The `user_database_lookup` tool requires a specific schema for the `user` table in the external PostgreSQL database. The table must contain a `tsv` column of type `tsvector` and a GIN index on this column.

You can add the necessary column and index to your `user` table by running the following SQL commands:

```sql
ALTER TABLE "user" ADD COLUMN tsv tsvector;
UPDATE "user" SET tsv = to_tsvector('english', coalesce(first_name, '') || ' ' || coalesce(last_name, '') || ' ' || coalesce(preferred_name, '') || ' ' || coalesce(title, '') || ' ' || coalesce(email, ''));
CREATE INDEX user_tsv_idx ON "user" USING gin(tsv);
```

These commands will:

1.  Add a new column `tsv` to your `user` table to store the pre-computed text search vectors.
2.  Populate this column with the combined text data from several other columns.
3.  Create a GIN index on the `tsv` column, which will make text searches much faster.

## 🧪 Development Status

### ✅ Completed (Phase 1: Foundation)
-   [x] Project structure and configuration
-   [x] Database models (User, Session, Message, Role)
-   [x] Authentication system with JWT
-   [x] User registration and login endpoints
-   [x] Role configuration system (YAML & DB loading)
-   [x] Docker setup
-   [x] Pydantic schemas
-   [x] Error handling and exceptions
-   [x] Core security utilities

### ✅ Completed (Phase 2: Session Management)
-   [x] Session management endpoints
-   [x] Redis integration for session caching
-   [x] Session CRUD operations (Create, Read, Update, Delete)
-   [x] Session context storage and retrieval
-   [x] Role listing and details endpoints
-   [x] Session naming constraints validation
-   [x] User session limits enforcement
-   [x] Database table auto-creation for development

### ✅ Completed (Phase 3: LLM Integration)
-   [x] LLM provider abstraction layer
-   [x] OpenAI GPT-4 integration
-   [x] Agent factory and memory management
-   [x] Role-based AI agent creation
-   [x] Conversation context persistence
-   [x] Streaming response support
-   [x] Chat API endpoints

### ✅ Completed (Phase 4: Real-time Communication)
-   [x] WebSocket implementation for real-time chat
-   [x] Streaming message protocol
-   [x] WebSocket authentication
-   [x] Connection management
-   [x] Real-time AI response streaming
-   [x] Error handling and reconnection support

### ✅ Completed (Phase 5: Tools & Framework)
-   [x] Tool framework architecture
-   [x] Math solver tool implementation
-   [x] General calculator tool
-   [x] Tool registry system
-   [x] Comprehensive testing suite
-   [x] Complete system integration

### ✅ Completed (Phase 6: Embedding Support)
-   [x] Embedding provider abstraction layer
-   [x] Azure OpenAI embedding provider
-   [x] OpenAI embedding provider
-   [x] Embedding service layer with similarity operations
-   [x] Text clustering and semantic search
-   [x] Embedding API endpoints
-   [x] Provider factory and configuration management
-   [x] Comprehensive embedding documentation

### 🚀 System Ready for Production
All core features implemented and tested!

## 🧪 Testing

A test script is provided to verify API functionality:

```bash
# Start the services
docker-compose up -d

# Wait for services to be ready, then run tests
python test_api.py

# Or test against a different URL
python test_api.py http://localhost:8000
```

The test script will verify:
-   Health check endpoint
-   External JWT token verification
-   Role listing
-   Session creation, listing, updating
-   Authentication and authorization

## 🚀 API Endpoints

### General
-   `GET /` - Root endpoint with API information.
-   `GET /health` - Health check endpoint.

### Authentication
-   `GET /api/v1/auth/verify` - Verify external JWT token and get user info.

### Session Management
-   `GET /api/v1/sessions/` - List user sessions.
-   `POST /api/v1/sessions/` - Create new session.
-   `GET /api/v1/sessions/{session_key}` - Get session details.
-   `PUT /api/v1/sessions/{session_key}` - Update session.
-   `DELETE /api/v1/sessions/{session_key}` - Delete session.

### Role Management
-   `GET /api/v1/roles/` - List available AI roles.
-   `GET /api/v1/roles/{role_name}` - Get role details.
-   `POST /api/v1/roles/` - Create a new role (admin only).
-   `PUT /api/v1/roles/{role_name}` - Update an existing role (admin only).
-   `DELETE /api/v1/roles/{role_name}` - Soft delete a role (admin only).

### Chat API
-   `POST /api/v1/chat/` - Send chat message (REST).
-   `GET /api/v1/chat/{session_key}/history` - Get message history.
-   `DELETE /api/v1/chat/{session_key}/history` - Clear message history.
-   `GET /api/v1/chat/{session_key}/agent-info` - Get agent information.

### Tool Management
-   `GET /api/v1/tools/` - List available tools.
-   `GET /api/v1/tools/{tool_name}` - Get tool details.

### Embedding API
-   `POST /api/v1/embeddings/` - Generate embeddings for single or multiple texts.
-   `POST /api/v1/embeddings/similarity` - Compute similarity between two texts.
-   `POST /api/v1/embeddings/search` - Find most similar texts from a collection.
-   `POST /api/v1/embeddings/cluster` - Cluster texts using K-means clustering.
-   `GET /api/v1/embeddings/provider/info` - Get current provider information and available providers.
-   `GET /api/v1/embeddings/provider/validate` - Validate that the current provider is working correctly.

### WebSocket
-   `WS /ws/chat?session_key={key}&token={jwt}` - Real-time streaming chat.

---

**Note**: This is a development version. For production use, ensure proper security configurations, rate limiting, and monitoring are in place.
