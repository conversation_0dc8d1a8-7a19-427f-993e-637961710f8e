# Tool Implementation Template

This document provides a template for implementing new tools in the Xapa AI system.

## Overview

Tools in the Xapa AI system follow a standardized interface defined in `app/agent/tools/base.py`. Each tool must implement the `BaseTool` abstract base class.

## Template Structure

```python
from typing import Dict, Any, Optional
from app.agent.tools.base import BaseTool, ToolSchema, ToolResult
import logging

logger = logging.getLogger(__name__)

class YourTool(BaseTool):
    """Your tool implementation."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the tool.
        
        Args:
            config: Optional configuration dictionary
        """
        super().__init__(config)
        # Initialize your tool with configuration
        # Example: self.api_key = config.get("api_key") if config else None
        
    @property
    def name(self) -> str:
        """
        Tool name identifier.
        
        Returns:
            str: Unique tool name
        """
        return "your_tool_name"
    
    @property
    def description(self) -> str:
        """
        Tool description.
        
        Returns:
            str: Tool description
        """
        return "Description of what your tool does"
    
    def get_schema(self) -> ToolSchema:
        """
        Get tool schema for LLM function calling.
        
        Returns:
            ToolSchema: Tool schema definition
        """
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "param1": {
                        "type": "string",
                        "description": "Description of param1"
                    },
                    "param2": {
                        "type": "integer",
                        "description": "Description of param2",
                        "default": 10
                    }
                },
                "required": ["param1"]
            }
        )
    
    async def execute(self, user_context: Optional[Dict[str, Any]] = None, **kwargs) -> ToolResult:
        """
        Execute the tool with given parameters and user context.
        
        Args:
            user_context: Optional user context containing user_id, tenant_id, client_id, session_key
            **kwargs: Tool parameters as defined in the schema
            
        Returns:
            ToolResult: Execution result
            
        Example:
            result = await tool.execute(
                user_context={"user_id": "123", "tenant_id": "abc"},
                param1="value1",
                param2=42
            )
        """
        try:
            # Extract parameters
            param1 = kwargs.get("param1")
            param2 = kwargs.get("param2", 10)  # Use default if not provided
            
            # Validate parameters if needed
            if not param1:
                return ToolResult.error_result("param1 is required")
            
            # Implement your tool logic here
            # This might involve:
            # 1. Making API calls
            # 2. Database queries
            # 3. File operations
            # 4. Computation
            # 5. etc.
            
            # Example placeholder implementation:
            result_data = {
                "param1": param1,
                "param2": param2,
                "output": f"Processed {param1} with value {param2}"
            }
            
            return ToolResult.success_result(
                result=result_data,
                metadata={
                    "tool_name": self.name,
                    "execution_time": "0.1s"  # Placeholder
                }
            )
            
        except Exception as e:
            logger.error(f"Error executing {self.name}: {str(e)}")
            return ToolResult.error_result(
                error=f"Tool execution failed: {str(e)}",
                metadata={
                    "tool_name": self.name,
                    "error_type": type(e).__name__
                }
            )

# Register the tool
# Add this at the end of your tool file to automatically register it
from app.agent.tools.base import tool_registry
tool_registry.register_tool(YourTool())
```

## File Location

Create your tool file in the `app/agent/tools/` directory:
```
app/agent/tools/your_tool.py
```

## Registration

The tool is automatically registered when the module is imported. The tool manager discovers and loads all tools from the tools directory.

## Usage

Once implemented, your tool will be available for use by roles. To make it available to a role:

1. Add the tool name to the role's tools list in the database
2. Or add it to a role configuration in the roles directory

## Best Practices

1. **Error Handling**: Always wrap tool logic in try/except blocks and return appropriate `ToolResult` objects
2. **Logging**: Use the provided logger to log important events and errors
3. **Validation**: Validate input parameters before processing
4. **User Context**: Use the user_context parameter to access user-specific information when needed
5. **Async Support**: Implement tools as async functions to avoid blocking the event loop
6. **Metadata**: Include useful metadata in ToolResult for debugging and monitoring
7. **Documentation**: Provide clear descriptions in the schema for LLM understanding
8. **Security**: Be cautious with user input and sanitize as needed
9. **Performance**: Consider caching or other optimizations for expensive operations
10. **Testing**: Write unit tests for your tool logic

## Example Tool

Here's a simple example of a calculator tool:

```python
from typing import Dict, Any, Optional
from app.agent.tools.base import BaseTool, ToolSchema, ToolResult
import logging

logger = logging.getLogger(__name__)

class CalculatorTool(BaseTool):
    """Tool for performing basic mathematical calculations."""
    
    @property
    def name(self) -> str:
        return "calculator"
    
    @property
    def description(self) -> str:
        return "Perform basic mathematical calculations"
    
    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "Mathematical expression to evaluate (e.g., '2 + 3 * 4')"
                    }
                },
                "required": ["expression"]
            }
        )
    
    async def execute(self, user_context: Optional[Dict[str, Any]] = None, **kwargs) -> ToolResult:
        try:
            expression = kwargs.get("expression", "").strip()
            
            if not expression:
                return ToolResult.error_result("No expression provided")
            
            # Safely evaluate the expression
            # Note: In production, use a proper math expression parser
            result = eval(expression)
            
            return ToolResult.success_result(
                result=result,
                metadata={
                    "expression": expression,
                    "operation": "calculation"
                }
            )
        except Exception as e:
            return ToolResult.error_result(
                error=str(e),
                metadata={"expression": expression}
            )

# Register the tool
from app.agent.tools.base import tool_registry
tool_registry.register_tool(CalculatorTool())