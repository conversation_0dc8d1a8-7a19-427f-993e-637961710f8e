# Feedback System

The feedback system allows users to provide feedback on AI responses, helping to improve the quality of interactions and gather user satisfaction data.

## Overview

The feedback system supports multiple types of feedback:
- **Thumbs Up/Down**: Simple positive/negative feedback
- **Rating**: 1-5 star rating system
- **Comments**: Detailed text feedback
- **Custom**: Extensible metadata for specific feedback aspects

## Database Schema

### Feedback Table

The `feedback` table stores all user feedback with the following structure:

```sql
CREATE TABLE feedback (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    session_id UUID NOT NULL REFERENCES sessions(id),
    message_id UUID NOT NULL REFERENCES messages(id),
    feedback_type VARCHAR(20) NOT NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    is_positive BOOLEAN,
    comment TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### Indexes

- `feedback_user_id_idx` on `user_id`
- `feedback_session_id_idx` on `session_id`
- `feedback_message_id_idx` on `message_id`
- `feedback_type_idx` on `feedback_type`

## API Endpoints

### Create Feedback
`POST /api/v1/feedback/`

Create new feedback for a message.

**Request Body:**
```json
{
    "message_id": "uuid",
    "feedback_type": "thumbs_up|thumbs_down|rating|comment",
    "rating": 1-5,  // Required for rating type
    "is_positive": true|false,  // Required for thumbs_up/thumbs_down
    "comment": "text",  // Required for comment type
    "metadata": {}  // Optional additional data
}
```

**Response:**
```json
{
    "id": "uuid",
    "user_id": "uuid",
    "session_id": "uuid",
    "message_id": "uuid",
    "feedback_type": "thumbs_up",
    "is_positive": true,
    "rating": null,
    "comment": null,
    "metadata": {},
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
}
```

### List Feedback
`GET /api/v1/feedback/`

List user's feedback with optional filtering.

**Query Parameters:**
- `skip`: Number of items to skip (default: 0)
- `limit`: Number of items to return (default: 100, max: 1000)
- `feedback_type`: Filter by feedback type
- `session_id`: Filter by session ID

### Get Feedback Statistics
`GET /api/v1/feedback/stats`

Get aggregated feedback statistics.

**Query Parameters:**
- `session_id`: Optional filter by session ID

**Response:**
```json
{
    "total_feedback": 150,
    "thumbs_up": 120,
    "thumbs_down": 30,
    "average_rating": 4.2,
    "total_ratings": 80,
    "total_comments": 45,
    "positive_percentage": 80.0
}
```

### Get Specific Feedback
`GET /api/v1/feedback/{feedback_id}`

Retrieve a specific feedback entry.

### Update Feedback
`PUT /api/v1/feedback/{feedback_id}`

Update existing feedback.

### Delete Feedback
`DELETE /api/v1/feedback/{feedback_id}`

Delete feedback entry.

## Feedback Types

### 1. Thumbs Up/Down
Simple binary feedback for quick user responses.

```json
{
    "message_id": "uuid",
    "feedback_type": "thumbs_up",
    "is_positive": true
}
```

### 2. Rating
1-5 star rating system for detailed evaluation.

```json
{
    "message_id": "uuid",
    "feedback_type": "rating",
    "rating": 4
}
```

### 3. Comment
Detailed text feedback for specific insights.

```json
{
    "message_id": "uuid",
    "feedback_type": "comment",
    "comment": "The response was helpful but could be more concise."
}
```

## Validation Rules

1. **Feedback Type**: Must be one of: `thumbs_up`, `thumbs_down`, `rating`, `comment`
2. **Rating**: Required for `rating` type, must be 1-5
3. **Is Positive**: Required for `thumbs_up`/`thumbs_down` types
4. **Comment**: Required for `comment` type, max 2000 characters
5. **One Feedback Per Message**: Users can only provide one feedback per message
6. **Message Access**: Users can only provide feedback on messages in their own sessions

## Usage Examples

### Frontend Integration

```javascript
// Thumbs up feedback
async function submitThumbsUp(messageId) {
    const response = await fetch('/api/v1/feedback/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            message_id: messageId,
            feedback_type: 'thumbs_up',
            is_positive: true
        })
    });
    return response.json();
}

// Rating feedback
async function submitRating(messageId, rating) {
    const response = await fetch('/api/v1/feedback/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            message_id: messageId,
            feedback_type: 'rating',
            rating: rating
        })
    });
    return response.json();
}
```

## Security Considerations

1. **Authentication**: All endpoints require valid JWT authentication
2. **Authorization**: Users can only access their own feedback
3. **Rate Limiting**: Consider implementing rate limiting for feedback creation
4. **Input Validation**: All inputs are validated using Pydantic schemas
5. **SQL Injection**: Protected by SQLAlchemy ORM

## Future Enhancements

1. **Feedback Analytics Dashboard**: Admin interface for feedback insights
2. **Feedback Categories**: Predefined categories for structured feedback
3. **Feedback Aggregation**: Aggregate feedback for AI model improvement
4. **Feedback Notifications**: Notify when feedback is received
5. **Bulk Feedback Operations**: Export/import feedback data
6. **Feedback Trends**: Track feedback trends over time
