{% extends "base.html" %}

{% block title %}Roles - Xapa AI{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="fas fa-users me-2"></i>
                    Role Management
                </h1>
                <div>
                    <a href="/roles/create" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        Create Role
                    </a>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <input type="text" class="form-control" id="searchRoles" placeholder="Search roles..." onkeyup="filterRoles()">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Roles Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="rolesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Image</th>
                                    <th>Name</th>
                                    <th>Display Name</th>
                                    <th>Description</th>
                                    <th>Tools</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Modified</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="rolesTableBody">
                                <!-- Roles will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Loading roles...</p>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the role "<span id="deleteRoleName"></span>"?</p>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="hardDelete">
                    <label class="form-check-label" for="hardDelete">
                        Permanently delete (cannot be undone)
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="/static/js/roles.js"></script>
<script>
// Debug function for browser console
window.debugRoles = function() {
    console.log('=== ROLES DEBUG INFO ===');
    console.log('rolesData:', rolesData);
    console.log('loadingModal element:', document.getElementById('loadingModal'));
    console.log('rolesTableBody element:', document.getElementById('rolesTableBody'));
    console.log('Bootstrap modal instance:', bootstrap.Modal.getInstance(document.getElementById('loadingModal')));

    // Test API call
    utils.apiGet('/roles').then(response => {
        console.log('Direct API test response:', response);
    }).catch(error => {
        console.error('Direct API test error:', error);
    });
};
</script>
{% endblock %}
