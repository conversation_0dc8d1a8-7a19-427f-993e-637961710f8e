// Main JavaScript file for Xapa AI

// Global variables
window.API_BASE = `${window.location.protocol}//${window.location.host}/api/v1`;

// Utility functions
function showAlert(message, type = 'info', duration = 5000) {
    const alertContainer = document.getElementById('alertContainer') || createAlertContainer();
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alertDiv);
    
    // Auto-dismiss after duration
    if (duration > 0) {
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, duration);
    }
}

function createAlertContainer() {
    const container = document.createElement('div');
    container.id = 'alertContainer';
    container.className = 'position-fixed top-0 end-0 p-3';
    container.style.zIndex = '1050';
    document.body.appendChild(container);
    return container;
}

let loadingStack = 0;
let hideTimeout = null;
let forceCloseTimeout = null;

function showLoading(show = true, message = 'Loading...') {
    const overlay = document.getElementById('loadingOverlay');
    if (!overlay) {
        console.warn('Loading overlay not found');
        return;
    }

    const messageElement = overlay.querySelector('p');
    if (messageElement) {
        messageElement.textContent = message;
    }

    // Always clear any pending hide timeout when the state is changed
    if (hideTimeout) {
        clearTimeout(hideTimeout);
        hideTimeout = null;
    }

    if (show) {
        loadingStack++;
        if (loadingStack === 1) {
            // Clear any lingering force-close timeout
            if (forceCloseTimeout) {
                clearTimeout(forceCloseTimeout);
            }

            overlay.classList.add('show');

            // Set a new safety timeout
            forceCloseTimeout = setTimeout(() => {
                console.warn('Force closing loading overlay after 30 seconds');
                loadingStack = 0;
                overlay.classList.remove('show');
            }, 30000);
        }
    } else {
        loadingStack--;
        if (loadingStack <= 0) {
            loadingStack = 0;

            // Delay hiding to prevent flickering for rapidly chained operations
            hideTimeout = setTimeout(() => {
                overlay.classList.remove('show');
                if (forceCloseTimeout) {
                    clearTimeout(forceCloseTimeout);
                    forceCloseTimeout = null;
                }
            }, 200); // 200ms delay
        }
    }
}

// Authentication functions
function getAuthToken() {
    // For API calls, we expect JWT tokens to be provided externally
    // This will be handled by the backend authentication system
    return localStorage.getItem('api_token') || null;
}

async function ensureAuthentication() {
    console.log('[ENSURE AUTH] Starting authentication process');
    
    // Ensure we have authentication token for API calls
    let token = getAuthToken();
    if (token) {
        console.log('[ENSURE AUTH] Found existing token');
        return token;
    }
    
    // Try to get token from web session (for Auth0 users)
    console.log('[ENSURE AUTH] Trying web session token...');
    try {
        const response = await fetch(`${API_BASE}/auth/token`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include', // Include cookies for session auth
        });
        
        if (response.ok) {
            const data = await response.json();
            const webToken = data.access_token;
            localStorage.setItem('api_token', webToken);
            console.log('[ENSURE AUTH] Web session token obtained');
            return webToken;
        } else {
            console.log('[ENSURE AUTH] Web session token failed:', response.status);
        }
    } catch (error) {
        console.log('[ENSURE AUTH] Web session token error:', error.message);
    }
    
    // No authentication available
    console.log('[ENSURE AUTH] No authentication available - user must login');
    throw new Error('Authentication required. Please login with Auth0.');
}

// API helper functions
const activeRequests = new Map();

async function apiRequest(url, options = {}) {
    console.log(`[API REQUEST] ${url}`);
    
    // Prevent duplicate concurrent requests to the same URL
    const requestKey = `${options.method || 'GET'}:${url}`;
    if (activeRequests.has(requestKey)) {
        console.log('- Duplicate request prevented:', requestKey);
        return activeRequests.get(requestKey);
    }
    
    let authToken = getAuthToken();
    console.log('- Initial auth token:', authToken ? 'EXISTS' : 'MISSING');
    
    // If no auth token, ensure authentication (Auth0 only)
    if (!authToken) {
        console.log('- Getting authentication token...');
        authToken = await ensureAuthentication();
        console.log('- Authentication result:', authToken ? 'SUCCESS' : 'FAILED');
    }
    
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
        },
    };

    // Add authorization header
    if (authToken) {
        defaultOptions.headers['Authorization'] = `Bearer ${authToken}`;
        console.log('- Authorization header added');
    } else {
        console.log('- WARNING: No authorization header will be added');
    }

    const mergedOptions = { ...defaultOptions, ...options };

    // Merge headers properly
    if (options.headers) {
        mergedOptions.headers = { ...defaultOptions.headers, ...options.headers };
    }

    try {
        // Add timeout to prevent hanging requests
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

        const requestPromise = fetch(`${API_BASE}${url}`, {
            ...mergedOptions,
            signal: controller.signal,
            redirect: 'follow' // Ensure redirects are followed
        });
        
        // Store the request promise to prevent duplicates
        activeRequests.set(requestKey, requestPromise.then(async response => {
            activeRequests.delete(requestKey);
            clearTimeout(timeoutId);
            return response;
        }).catch(error => {
            activeRequests.delete(requestKey);
            clearTimeout(timeoutId);
            throw error;
        }));
        
        const response = await activeRequests.get(requestKey);

        // Handle redirects manually if needed
        if (response.status === 307 && response.headers.get('location')) {
            const redirectUrl = response.headers.get('location');
            console.log('Handling 307 redirect to:', redirectUrl);
            
            // Make the redirected request with the same headers
            const redirectResponse = await fetch(redirectUrl, {
                ...mergedOptions,
                signal: controller.signal
            });
            
            if (!redirectResponse.ok) {
                if (redirectResponse.status === 401) {
                    console.log('Authentication failed on redirect - clearing stored token');
                    localStorage.removeItem('api_token');
                }
                const errorData = await redirectResponse.json().catch(() => ({}));
                throw new Error(errorData.detail || `HTTP ${redirectResponse.status}: ${redirectResponse.statusText}`);
            }
            
            return await redirectResponse.json();
        }

        if (!response.ok) {
            // If unauthorized, clear any stored token
            if (response.status === 401) {
                console.log('Authentication failed - clearing stored token');
                localStorage.removeItem('api_token');
            }

            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    } catch (error) {
        if (error.name === 'AbortError') {
            throw new Error('Request timeout - please try again');
        }
        console.error('API Request failed:', error);
        throw error;
    }
}

async function apiGet(url) {
    return apiRequest(url, { method: 'GET' });
}

async function apiPost(url, data) {
    return apiRequest(url, {
        method: 'POST',
        body: JSON.stringify(data),
    });
}

async function apiPut(url, data) {
    return apiRequest(url, {
        method: 'PUT',
        body: JSON.stringify(data),
    });
}

async function apiDelete(url) {
    return apiRequest(url, { method: 'DELETE' });
}

// Form validation helpers
function validateForm(formElement) {
    const inputs = formElement.querySelectorAll('input[required], textarea[required], select[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

function clearFormValidation(formElement) {
    const inputs = formElement.querySelectorAll('.is-invalid');
    inputs.forEach(input => {
        input.classList.remove('is-invalid');
    });
}

// Date formatting
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    // Parse UTC datetime from database
    let utcDate = new Date(dateString);
    
    // Get user's timezone and format in local time
    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    
    return utcDate.toLocaleDateString([], {timeZone: userTimezone}) + ' ' + 
           utcDate.toLocaleTimeString([], { 
               timeZone: userTimezone,
               hour: '2-digit', 
               minute: '2-digit' 
           });
}

function formatRelativeTime(dateString) {
    if (!dateString) return 'N/A';
    
    // Parse UTC datetime from database
    let utcDate = new Date(dateString);
    
    // Get user's timezone
    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    
    // Convert UTC to user's local timezone
    const localDate = new Date(utcDate.toLocaleString("en-US", {timeZone: userTimezone}));
    const now = new Date();
    
    const diffMs = now - localDate;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    
    return formatDate(dateString);
}

// Text utilities
function truncateText(text, maxLength = 100) {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Local storage helpers
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
        console.warn('Failed to save to localStorage:', error);
    }
}

function loadFromLocalStorage(key, defaultValue = null) {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.warn('Failed to load from localStorage:', error);
        return defaultValue;
    }
}

// Initialize tooltips and popovers
document.addEventListener('DOMContentLoaded', function() {
    // Check for existing authentication token
    const authToken = getAuthToken();
    if (!authToken) {
        console.log('No authentication token found. API calls will require authentication.');
    }

    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize Bootstrap popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Add fade-in animation to main content
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }

    
});

// Handle navigation active states
document.addEventListener('DOMContentLoaded', function() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.startsWith(href) && href !== '/') {
            link.classList.add('active');
        }
    });
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection:', event.reason);

    // Don't show alerts for common promise rejections
    const ignoredRejections = [
        'AbortError',
        'The user aborted a request'
    ];

    if (!ignoredRejections.some(ignored => event.reason && event.reason.toString().includes(ignored))) {
        showAlert('An unexpected error occurred. Please try again.', 'danger');
    }
});


// Export functions for use in other scripts
window.utils = {
    showAlert,
    showLoading,
    apiGet,
    apiPost,
    apiPut,
    apiDelete,
    validateForm,
    clearFormValidation,
    formatDate,
    formatRelativeTime,
    truncateText,
    escapeHtml,
    saveToLocalStorage,
    loadFromLocalStorage,
    ensureAuthentication
};

// Add CSRF token support if needed
function getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : null;
}