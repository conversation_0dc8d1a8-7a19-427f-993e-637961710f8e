from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
import uuid


class FeedbackBase(BaseModel):
    """Base feedback schema."""
    feedback_type: str = Field(..., description="Type of feedback: 'thumbs_up', 'thumbs_down', 'rating', 'comment'")
    rating: Optional[int] = Field(None, ge=1, le=5, description="Rating from 1-5 (optional)")
    is_positive: Optional[bool] = Field(None, description="Boolean feedback for thumbs up/down")
    comment: Optional[str] = Field(None, max_length=2000, description="Text feedback/comments")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")

    @validator('feedback_type')
    def validate_feedback_type(cls, v):
        allowed_types = ['thumbs_up', 'thumbs_down', 'rating', 'comment']
        if v not in allowed_types:
            raise ValueError(f"feedback_type must be one of: {', '.join(allowed_types)}")
        return v

    @validator('rating')
    def validate_rating_with_type(cls, v, values):
        feedback_type = values.get('feedback_type')
        if feedback_type == 'rating' and v is None:
            raise ValueError("rating is required when feedback_type is 'rating'")
        if feedback_type != 'rating' and v is not None:
            raise ValueError("rating should only be provided when feedback_type is 'rating'")
        return v

    @validator('is_positive')
    def validate_is_positive_with_type(cls, v, values):
        feedback_type = values.get('feedback_type')
        if feedback_type in ['thumbs_up', 'thumbs_down'] and v is None:
            raise ValueError("is_positive is required for thumbs_up/thumbs_down feedback")
        if feedback_type not in ['thumbs_up', 'thumbs_down'] and v is not None:
            raise ValueError("is_positive should only be provided for thumbs_up/thumbs_down feedback")
        return v

    @validator('comment')
    def validate_comment_with_type(cls, v, values):
        feedback_type = values.get('feedback_type')
        if feedback_type == 'comment' and (not v or v.strip() == ''):
            raise ValueError("comment is required when feedback_type is 'comment'")
        return v


class FeedbackCreate(FeedbackBase):
    """Schema for creating feedback."""
    message_id: Union[str, uuid.UUID] = Field(..., description="ID of the message being rated")


class FeedbackUpdate(BaseModel):
    """Schema for updating feedback."""
    rating: Optional[int] = Field(None, ge=1, le=5, description="Updated rating")
    is_positive: Optional[bool] = Field(None, description="Updated boolean feedback")
    comment: Optional[str] = Field(None, max_length=2000, description="Updated comment")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Updated metadata")


class FeedbackResponse(FeedbackBase):
    """Schema for feedback response."""
    id: Union[str, uuid.UUID]
    user_id: Union[str, uuid.UUID]
    session_id: Union[str, uuid.UUID]
    message_id: Union[str, uuid.UUID]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
        json_encoders = {
            uuid.UUID: str
        }

    @classmethod
    def from_orm(cls, obj):
        """Custom from_orm to handle field mapping."""
        return cls(
            id=obj.id,
            user_id=obj.user_id,
            session_id=obj.session_id,
            message_id=obj.message_id,
            feedback_type=obj.feedback_type,
            rating=obj.rating,
            is_positive=obj.is_positive,
            comment=obj.comment,
            metadata=obj.feedback_metadata or {},
            created_at=obj.created_at,
            updated_at=obj.updated_at
        )

    def model_dump(self, **kwargs):
        """Override to ensure UUIDs are always converted to strings."""
        data = super().model_dump(**kwargs)
        for field in ['id', 'user_id', 'session_id', 'message_id']:
            if isinstance(data.get(field), uuid.UUID):
                data[field] = str(data[field])
        return data


class FeedbackListResponse(BaseModel):
    """Schema for feedback list response."""
    feedback: List[FeedbackResponse]
    total: int
    page: int
    page_size: int


class MessageContext(BaseModel):
    """Schema for message context in feedback."""
    id: Union[str, uuid.UUID]
    role: str  # 'user', 'assistant', 'system'
    content: str
    sequence_number: int
    created_at: datetime

    class Config:
        from_attributes = True
        json_encoders = {
            uuid.UUID: str
        }


class FeedbackWithContext(FeedbackResponse):
    """Schema for feedback response with message context."""
    agent_message: MessageContext  # The AI response being rated
    user_question: Optional[MessageContext] = None  # The preceding user question
    conversation_context: Optional[List[MessageContext]] = None  # Recent conversation history


class FeedbackStatsResponse(BaseModel):
    """Schema for feedback statistics response."""
    total_feedback: int
    thumbs_up: int
    thumbs_down: int
    average_rating: Optional[float]
    total_ratings: int
    total_comments: int
    positive_percentage: Optional[float]
