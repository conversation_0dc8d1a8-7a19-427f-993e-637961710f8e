

# XAPA Content Publishing Logic Documentation

## Overview

The XAPA backend implements a sophisticated multi-tenant content publishing system that distributes educational content from a central CMS to individual client databases. This system supports versioning, incremental updates, and comprehensive audit trails.

---

## Architecture

### Multi-Tenant Database Structure

- **Master Database ("global")**: Central repository for all content creation and management
- **Client Databases**: Individual isolated databases for each client (identified by `client.id_key`)
- **Database Sessions:**
  - `g.db_session` — Global database session
  - `g.client_db_session` — Current client database session
  - `get_db_session(client_key)` — Specific client database session

---

## Content Hierarchy

```
Package (Top Level)
├── Xperiences
│   └── Quests
│       └── Nodes
│           ├── Node Options
│           ├── Node Transcripts
│           ├── Node Branches
│           └── Node Option Matching
└── Programs
    └── Quests (same structure as above)
```

---

## Content Publishing Workflow

### 1. Package Publishing

**API:**
```http
PUT /api/cms/packages/{id}/publish
```

**Process:**
```python
def publish_package():
    # 1. Get package and associated clients
    package = g.db_session.query(Package).filter(Package.id == id).first()
    clients = package.clients

    # 2. Check for incremental updates
    last_publish_log = g.db_session.query(ContentPublishLog).filter(
        ContentPublishLog.content_id == package.id,
        ContentPublishLog.content_type == 'Package'
    ).order_by(ContentPublishLog.date_published.desc()).first()

    # 3. Release content to clients
    release_package_to_clients(package, clients, last_publish_date)

    # 4. Log publish event
    log = ContentPublishLog(
        content_id=package.id,
        content_type='Package',
        user_id=g.user_id
    )
    g.db_session.add(log)
    g.db_session.commit()
```

### 2. Content Release Functions

#### Quest Release (`release_quest`)
```python
def release_quest(quest, is_latest=True):
    # 1. Mark previous versions as not latest
    if is_latest:
        g.client_db_session.query(PublishedQuest).filter(
            PublishedQuest.quest_id == quest.id,
            PublishedQuest.is_latest == True
        ).update({"is_latest": False})

    # 2. Create new published version
    new_quest = PublishedQuest()
    # Copy all attributes except 'id'
    for column in inspect(new_quest).mapper.column_attrs:
        key = column.key
        if hasattr(quest, key) and key != "id":
            setattr(new_quest, key, getattr(quest, key))

    # 3. Set version info
    new_quest.revision = last_revision + 1
    new_quest.quest_id = quest.id
    new_quest.is_latest = is_latest

    # 4. Publish all related content
    publish_quest_nodes(quest, new_quest)
    publish_quest_options(quest, new_quest)
    publish_quest_transcripts(quest, new_quest)
    publish_quest_branches(quest, new_quest)
```

#### Xperience Release (`release_xperience`)
```python
def release_xperience(xperience, is_latest=True):
    # 1. Version management
    if is_latest:
        g.client_db_session.query(PublishedXperience).filter(
            PublishedXperience.xperience_id == xperience.id,
            PublishedXperience.is_latest == True
        ).update({"is_latest": False})

    # 2. Create published version
    new_xperience = PublishedXperience()
    # Copy attributes and set version info

    # 3. Update associated quest IDs
    quests = g.client_db_session.query(Quest).join(XperienceQuestAssociation).filter(
        XperienceQuestAssociation.xperience_id == xperience.id
    ).all()
    quest_ids = [quest.id for quest in quests]
    new_xperience.quest_ids = json.dumps(quest_ids)
```

#### Program Release (`release_program`)
```python
def release_program(program, is_latest=True):
    # Similar pattern to xperience
    # 1. Version management
    # 2. Create published version
    # 3. Update associated quest IDs
    quests = g.client_db_session.query(Quest).join(ProgramQuestAssociation).filter(
        ProgramQuestAssociation.program_id == program.id
    ).all()
    quest_ids = [quest.id for quest in quests]
    new_program.quest_ids = json.dumps(quest_ids)
```

---

## Database Schema

### Published Content Tables
```sql
-- Published versions with versioning
CREATE TABLE published_quest (
    id UUID PRIMARY KEY,
    quest_id UUID NOT NULL,
    revision INTEGER NOT NULL,
    is_latest BOOLEAN DEFAULT FALSE,
    -- All original quest fields copied here
    date_created TIMESTAMP,
    date_updated TIMESTAMP
);

CREATE TABLE published_xperience (
    id UUID PRIMARY KEY,
    xperience_id UUID NOT NULL,
    revision INTEGER NOT NULL,
    is_latest BOOLEAN DEFAULT FALSE,
    quest_ids TEXT, -- JSON array of quest IDs
    -- All original xperience fields copied here
);

CREATE TABLE published_program (
    id UUID PRIMARY KEY,
    program_id UUID NOT NULL,
    revision INTEGER NOT NULL,
    is_latest BOOLEAN DEFAULT FALSE,
    quest_ids TEXT, -- JSON array of quest IDs
    -- All original program fields copied here
);
```

### Tracking Tables
```sql
-- Content publish logging
CREATE TABLE content_publish_log (
    id UUID PRIMARY KEY,
    content_id UUID NOT NULL,
    content_type VARCHAR(50) NOT NULL, -- 'Package', 'Xperience', 'Quest', etc.
    user_id UUID,
    user_name VARCHAR(255),
    version INTEGER,
    backup_path TEXT,
    date_published TIMESTAMP DEFAULT NOW()
);

-- Client-package associations
CREATE TABLE client_package_association (
    id UUID PRIMARY KEY,
    client_id UUID NOT NULL,
    package_id UUID NOT NULL,
    date_created TIMESTAMP,
    date_updated TIMESTAMP
);
```

---

## Versioning System

### Version Management

- **Revision Numbers:** Incremental integers for each published version
- **Latest Flag:** `is_latest = True` marks the current active version
- **Audit Trail:** Complete history maintained in published tables

### Version Queries
```python
# Get latest version for clients
latest_content = g.client_db_session.query(PublishedQuest).filter(
    PublishedQuest.quest_id == quest_id,
    PublishedQuest.is_latest == True
).first()

# Get specific revision
specific_version = g.client_db_session.query(PublishedQuest).filter(
    PublishedQuest.quest_id == quest_id,
    PublishedQuest.revision == 5
).first()

# Master tenant gets latest revision
if g.tenant_id == MASTER_TENANT:
    quest = g.db_session.query(PublishedQuest).filter_by(
        quest_id=id
    ).order_by(PublishedQuest.revision.desc()).first()
```

---

## Client Distribution

### Package-to-Client Publishing
```python
def release_package_to_clients(package, clients=[], last_publish_date=None):
    # 1. Get source content
    source_db_session = get_db_session()

    # 2. Determine updated content
    if last_publish_date:
        updated_xperiences = source_db_session.query(Xperience).filter(
            Xperience.date_updated > last_publish_date,
            Xperience.packages.any(Package.id == package.id)
        ).all()
    else:
        updated_xperiences = package.xperiences

    # 3. Publish to each client
    for client in clients:
        set_client_db_session(client.id_key)

        # Release quests first
        for quest in all_quests:
            release_quest(quest)

        # Then xperiences
        for xperience in updated_xperiences:
            release_xperience(xperience)

        # Finally programs
        for program in updated_programs:
            release_program(program)
```

---

## Database Structure Synchronization
```python
def publish_database_structure(client_key):
    # 1. Connect to both databases
    source_engine = create_engine(base_url + 'global')
    target_engine = create_engine(base_url + client_key)

    # 2. Compare schemas
    source_metadata = MetaData()
    source_metadata.reflect(bind=source_engine)
    target_metadata = MetaData()
    target_metadata.reflect(bind=target_engine)

    # 3. Generate update statements
    # - Create new tables
    # - Add/modify columns
    # - Update indexes and constraints

    # 4. Execute updates
    with target_engine.connect() as connection:
        for statement in update_statements:
            connection.execute(text(statement))
```

---

## Content Tracking & Logging

### Publish Logging
```python
def log_publish_event(content_id, content_type, user_id, backup_path=None):
    log = ContentPublishLog(
        content_id=content_id,
        content_type=content_type,
        user_id=user_id,
        user_name=g.user_name,
        version=get_next_version(),
        backup_path=backup_path,
        date_published=datetime.utcnow()
    )
    g.db_session.add(log)
    g.db_session.commit()
```

### Content Backup

- Published content is backed up to Azure Storage as JSON
- Backup path stored in `ContentPublishLog`
- Enables rollback capabilities

---

## API Endpoints

### Publishing Endpoints
```
PUT /api/cms/packages/{id}/publish         # Publish package to clients
PUT /api/cms/xperiences/{id}/status        # Publish xperience (when status = "Published")
PUT /api/cms/programs/{id}/status          # Publish program (when status = "Published")
```

### Content Retrieval Endpoints
```
GET /api/app/quest/{id}                    # Get latest published quest
GET /api/app/xperience/{id}                # Get latest published xperience
GET /api/app/program/{id}                  # Get latest published program
```

### Client Management
```
PUT /api/cms/clients/{id}/packages         # Assign packages to client
GET /api/cms/clients/{id}/sync             # Sync client database and content
```

---

## Key Features

### Incremental Publishing

- Only publishes content changed since last publish date
- Reduces processing time and database load
- Maintains consistency across versions

### Multi-Tenant Security

- Complete data isolation between clients
- Secure database session management
- Client-specific content filtering

### Comprehensive Audit Trail

- All publishing events logged with user attribution
- Version history maintained indefinitely
- Backup storage for rollback capabilities

---

This publishing system provides a robust, scalable foundation for distributing educational content while maintaining data integrity, security, and comprehensive tracking capabilities.

  Content Hierarchy

  Package (Top Level)
  ├── Xperiences
  │   └── Quests
  │       └── Nodes
  │           ├── Node Options
  │           ├── Node Transcripts
  │           ├── Node Branches
  │           └── Node Option Matching
  └── Programs
      └── Quests (same structure as above)

  Content Publishing Workflow

  1. Package Publishing

  API: PUT /api/cms/packages/{id}/publish

  Process:
  def publish_package():
      # 1. Get package and associated clients
      package = g.db_session.query(Package).filter(Package.id == id).first()
      clients = package.clients

      # 2. Check for incremental updates
      last_publish_log = g.db_session.query(ContentPublishLog).filter(
          ContentPublishLog.content_id == package.id,
          ContentPublishLog.content_type == 'Package'
      ).order_by(ContentPublishLog.date_published.desc()).first()

      # 3. Release content to clients
      release_package_to_clients(package, clients, last_publish_date)

      # 4. Log publish event
      log = ContentPublishLog(
          content_id=package.id,
          content_type='Package',
          user_id=g.user_id
      )
      g.db_session.add(log)
      g.db_session.commit()

  2. Content Release Functions

  Quest Release (release_quest)

  def release_quest(quest, is_latest=True):
      # 1. Mark previous versions as not latest
      if is_latest:
          g.client_db_session.query(PublishedQuest).filter(
              PublishedQuest.quest_id == quest.id,
              PublishedQuest.is_latest == True
          ).update({"is_latest": False})

      # 2. Create new published version
      new_quest = PublishedQuest()
      # Copy all attributes except 'id'
      for column in inspect(new_quest).mapper.column_attrs:
          key = column.key
          if hasattr(quest, key) and key != "id":
              setattr(new_quest, key, getattr(quest, key))

      # 3. Set version info
      new_quest.revision = last_revision + 1
      new_quest.quest_id = quest.id
      new_quest.is_latest = is_latest

      # 4. Publish all related content
      publish_quest_nodes(quest, new_quest)
      publish_quest_options(quest, new_quest)
      publish_quest_transcripts(quest, new_quest)
      publish_quest_branches(quest, new_quest)

  Xperience Release (release_xperience)

  def release_xperience(xperience, is_latest=True):
      # 1. Version management
      if is_latest:
          g.client_db_session.query(PublishedXperience).filter(
              PublishedXperience.xperience_id == xperience.id,
              PublishedXperience.is_latest == True
          ).update({"is_latest": False})

      # 2. Create published version
      new_xperience = PublishedXperience()
      # Copy attributes and set version info

      # 3. Update associated quest IDs
      quests = g.client_db_session.query(Quest).join(XperienceQuestAssociation).filter(
          XperienceQuestAssociation.xperience_id == xperience.id
      ).all()
      quest_ids = [quest.id for quest in quests]
      new_xperience.quest_ids = json.dumps(quest_ids)

  Program Release (release_program)

  def release_program(program, is_latest=True):
      # Similar pattern to xperience
      # 1. Version management
      # 2. Create published version
      # 3. Update associated quest IDs
      quests = g.client_db_session.query(Quest).join(ProgramQuestAssociation).filter(
          ProgramQuestAssociation.program_id == program.id
      ).all()
      quest_ids = [quest.id for quest in quests]
      new_program.quest_ids = json.dumps(quest_ids)

  Database Schema

  Published Content Tables

  -- Published versions with versioning
  CREATE TABLE published_quest (
      id UUID PRIMARY KEY,
      quest_id UUID NOT NULL,
      revision INTEGER NOT NULL,
      is_latest BOOLEAN DEFAULT FALSE,
      -- All original quest fields copied here
      date_created TIMESTAMP,
      date_updated TIMESTAMP
  );

  CREATE TABLE published_xperience (
      id UUID PRIMARY KEY,
      xperience_id UUID NOT NULL,
      revision INTEGER NOT NULL,
      is_latest BOOLEAN DEFAULT FALSE,
      quest_ids TEXT, -- JSON array of quest IDs
      -- All original xperience fields copied here
  );

  CREATE TABLE published_program (
      id UUID PRIMARY KEY,
      program_id UUID NOT NULL,
      revision INTEGER NOT NULL,
      is_latest BOOLEAN DEFAULT FALSE,
      quest_ids TEXT, -- JSON array of quest IDs
      -- All original program fields copied here
  );

  Tracking Tables

  -- Content publish logging
  CREATE TABLE content_publish_log (
      id UUID PRIMARY KEY,
      content_id UUID NOT NULL,
      content_type VARCHAR(50) NOT NULL, -- 'Package', 'Xperience', 'Quest', etc.
      user_id UUID,
      user_name VARCHAR(255),
      version INTEGER,
      backup_path TEXT,
      date_published TIMESTAMP DEFAULT NOW()
  );

  -- Client-package associations
  CREATE TABLE client_package_association (
      id UUID PRIMARY KEY,
      client_id UUID NOT NULL,
      package_id UUID NOT NULL,
      date_created TIMESTAMP,
      date_updated TIMESTAMP
  );

  Versioning System

  Version Management

  - Revision Numbers: Incremental integers for each published version
  - Latest Flag: is_latest = True marks the current active version
  - Audit Trail: Complete history maintained in published tables

  Version Queries

  # Get latest version for clients
  latest_content = g.client_db_session.query(PublishedQuest).filter(
      PublishedQuest.quest_id == quest_id,
      PublishedQuest.is_latest == True
  ).first()

  # Get specific revision
  specific_version = g.client_db_session.query(PublishedQuest).filter(
      PublishedQuest.quest_id == quest_id,
      PublishedQuest.revision == 5
  ).first()

  # Master tenant gets latest revision
  if g.tenant_id == MASTER_TENANT:
      quest = g.db_session.query(PublishedQuest).filter_by(
          quest_id=id
      ).order_by(PublishedQuest.revision.desc()).first()

  Client Distribution

  Package-to-Client Publishing

  def release_package_to_clients(package, clients=[], last_publish_date=None):
      # 1. Get source content
      source_db_session = get_db_session()

      # 2. Determine updated content
      if last_publish_date:
          updated_xperiences = source_db_session.query(Xperience).filter(
              Xperience.date_updated > last_publish_date,
              Xperience.packages.any(Package.id == package.id)
          ).all()
      else:
          updated_xperiences = package.xperiences

      # 3. Publish to each client
      for client in clients:
          set_client_db_session(client.id_key)

          # Release quests first
          for quest in all_quests:
              release_quest(quest)

          # Then xperiences
          for xperience in updated_xperiences:
              release_xperience(xperience)

          # Finally programs
          for program in updated_programs:
              release_program(program)

  Database Structure Synchronization

  def publish_database_structure(client_key):
      # 1. Connect to both databases
      source_engine = create_engine(base_url + 'global')
      target_engine = create_engine(base_url + client_key)

      # 2. Compare schemas
      source_metadata = MetaData()
      source_metadata.reflect(bind=source_engine)
      target_metadata = MetaData()
      target_metadata.reflect(bind=target_engine)

      # 3. Generate update statements
      # - Create new tables
      # - Add/modify columns
      # - Update indexes and constraints

      # 4. Execute updates
      with target_engine.connect() as connection:
          for statement in update_statements:
              connection.execute(text(statement))

  Content Tracking & Logging

  Publish Logging

  def log_publish_event(content_id, content_type, user_id, backup_path=None):
      log = ContentPublishLog(
          content_id=content_id,
          content_type=content_type,
          user_id=user_id,
          user_name=g.user_name,
          version=get_next_version(),
          backup_path=backup_path,
          date_published=datetime.utcnow()
      )
      g.db_session.add(log)
      g.db_session.commit()

  Content Backup

  - Published content is backed up to Azure Storage as JSON
  - Backup path stored in ContentPublishLog
  - Enables rollback capabilities

  API Endpoints

  Publishing Endpoints

  - PUT /api/cms/packages/{id}/publish - Publish package to clients
  - PUT /api/cms/xperiences/{id}/status - Publish xperience (when status = "Published")
  - PUT /api/cms/programs/{id}/status - Publish program (when status = "Published")

  Content Retrieval Endpoints

  - GET /api/app/quest/{id} - Get latest published quest
  - GET /api/app/xperience/{id} - Get latest published xperience
  - GET /api/app/program/{id} - Get latest published program

  Client Management

  - PUT /api/cms/clients/{id}/packages - Assign packages to client
  - GET /api/cms/clients/{id}/sync - Sync client database and content

  Key Features

  Incremental Publishing

  - Only publishes content changed since last publish date
  - Reduces processing time and database load
  - Maintains consistency across versions

  Multi-Tenant Security

  - Complete data isolation between clients
  - Secure database session management
  - Client-specific content filtering

  Comprehensive Audit Trail

  - All publishing events logged with user attribution
  - Version history maintained indefinitely
  - Backup storage for rollback capabilities

  This publishing system provides a robust, scalable foundation for distributing educational content while maintaining        
  data integrity, security, and comprehensive tracking capabilities.