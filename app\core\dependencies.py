from fastapi import Depends, Request, HTTPException, status
from fastapi.responses import RedirectResponse
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session
from typing import Optional
import uuid
from datetime import timedelta

from app.models.base import get_db
from app.models.user import User
from app.core.security import verify_token, create_access_token
from app.core.exceptions import AuthenticationError

# OAuth2 scheme for token authentication
# Note: tokenUrl is not used since we accept external JWT tokens
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token", auto_error=True)


async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> User:
    """
    Get current authenticated user from JWT token.
    If the user does not exist, create a new one.
    
    Args:
        token: JWT token from request header
        db: Database session
        
    Returns:
        User: Authenticated user object
        
    Raises:
        AuthenticationError: If token is invalid or user not found
    """
    # Verify token
    payload = verify_token(token)
    if not payload:
        raise AuthenticationError("Invalid authentication token")
    
    # Extract user ID from token
    user_id_str = payload.get("sub")
    if not user_id_str:
        raise AuthenticationError("Invalid token payload")
    
    # Extract tenant_id from token
    tenant_id = payload.get("iss", "global")
    
    try:
        user_id = uuid.UUID(user_id_str)
    except ValueError:
        raise AuthenticationError("Invalid user ID in token")
    
    # Get user from database
    user = db.query(User).filter(User.id == user_id).first()
    
    if not user:
        # User does not exist, create a new one
        new_user = User(
            id=user_id,
            username=f"user_{user_id_str[:8]}",
            email=f"{user_id_str}@example.com",
            password_hash="not_set", # User created via token, no password
            tenant_id=tenant_id,
            is_active=True
        )
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        return new_user

    if not user.is_active:
        raise AuthenticationError("User account is disabled")
    
    return user


# Web Authentication Dependencies (for Auth0)
async def get_current_web_user(
    request: Request,
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    Get current authenticated user from web session (Auth0).
    This is used for web interface authentication.

    Args:
        request: FastAPI request object containing session
        db: Database session

    Returns:
        Optional[User]: Authenticated user object or None if not authenticated
    """
    # Check if user is authenticated via session
    user_info = request.session.get('user')
    if not user_info:
        return None
    
    print(f"User info from session: {user_info}")

    user_email = user_info.get('email')
    if not user_email:
        return None

    try:
        # For Auth0 users, use the full Auth0 ID as a string identifier
        # We'll need to find or create user by Auth0 ID
        user = db.query(User).filter(User.email == user_email).first()

        if not user:
            # Create new user from Auth0 profile
            user = User(
                id=uuid.uuid4(),  # Generate new UUID for our system
                username=user_info.get('nickname', user_info.get('name', f"user_{uuid.uuid4().hex[:8]}")),
                email=user_email,
                password_hash="auth0_user",  # Auth0 users don't have local passwords
                is_active=True
            )
            db.add(user)
            db.commit()
            db.refresh(user)

        return user

    except (ValueError, TypeError):
        return None
