{% extends "base.html" %}

{% block title %}Test Agent - Xapa AI{% endblock %}

{% block extra_head %}
<style>
.message.streaming .typing-indicator {
    animation: blink 1s infinite;
    color: var(--primary-color);
    font-weight: bold;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}


.message.system {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    text-align: center;
    font-style: italic;
    margin: 0 auto;
    max-width: 60%;
    border-radius: 0.5rem;
}

.message.tool {
    background: var(--primary-alpha-10);
    border: 1px solid var(--primary-alpha-25);
    border-left: 4px solid var(--primary-color);
    margin: 0.5rem auto;
    max-width: 90%;
    border-radius: 0.75rem;
    box-shadow: 0 2px 8px var(--primary-alpha-15);
}

.tool-call-info {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.5rem;
    margin: 0.5rem 0;
    font-family: monospace;
    font-size: 0.875rem;
}

.tool-call-header {
    cursor: pointer;
    user-select: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
}

.tool-call-header:hover {
    background-color: var(--primary-alpha-10);
    border-radius: 0.25rem;
}

.tool-call-content {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #dee2e6;
}

.tool-call-content.collapsed {
    display: none;
}

.collapse-icon {
    transition: transform 0.2s ease;
}

.collapse-icon.collapsed {
    transform: rotate(-90deg);
}

.tool-indicator {
    background: var(--gradient-primary);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
    display: inline-block;
    font-weight: 600;
}

.message-time {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.message-tokens {
    font-size: 0.7rem;
    color: #0066cc;
    background-color: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 10px;
    padding: 0.15rem 0.5rem;
    margin-left: 0.5rem;
    font-weight: 500;
    white-space: nowrap;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
}

/* Markdown styling for messages */
.message-content h1, .message-content h2, .message-content h3, 
.message-content h4, .message-content h5, .message-content h6 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.message-content h1 { font-size: 1.5rem; }
.message-content h2 { font-size: 1.3rem; }
.message-content h3 { font-size: 1.1rem; }
.message-content h4 { font-size: 1rem; }

.message-content code {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 0.1rem 0.3rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    color: #e83e8c;
}

.message-content pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 0.5rem 0;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
}

.message-content pre code {
    background: none;
    border: none;
    padding: 0;
    color: inherit;
}

.message-content blockquote {
    border-left: 4px solid var(--primary-color);
    background-color: #f8f9fa;
    margin: 0.5rem 0;
    padding: 0.5rem 1rem;
    border-radius: 0 0.25rem 0.25rem 0;
}

.message-content ul, .message-content ol {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.message-content li {
    margin: 0.25rem 0;
}

.message-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 0.5rem 0;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    overflow: hidden;
}

.message-content th, .message-content td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.message-content th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.message-content a {
    color: var(--primary-color);
    text-decoration: none;
}

.message-content a:hover {
    text-decoration: underline;
}

.message-content strong {
    font-weight: 600;
}

.message-content em {
    font-style: italic;
}

.message-content hr {
    border: none;
    border-top: 1px solid #dee2e6;
    margin: 1rem 0;
}

/* Session list item hover effect */
.session-item-clickable {
    transition: background-color 0.2s ease;
}

.session-item-clickable:hover {
    background-color: #f8f9fa;
}

/* Session styling improvements */
.session-name {
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.2;
}

.role-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.role-badge.bg-primary {
    background-color: #4a90e2 !important;
    border: 1px solid #3a7bd5;
}

.role-badge.bg-secondary {
    background-color: #6c757d !important;
    border: 1px solid #5a6268;
}

.delete-session-btn {
    border: none;
    background: none;
    color: #dc3545;
    padding: 0.375rem 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.delete-session-btn:hover {
    background-color: #dc3545;
    color: white;
    opacity: 1;
    transform: scale(1.05);
}

.delete-session-btn:active {
    transform: scale(0.95);
}

/* Active session styling */
.list-group-item.border-primary {
    border-left: 4px solid #007bff !important;
    background-color: #f8f9ff;
}

.list-group-item.border-primary .session-name {
    color: #007bff;
    font-weight: 700;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>
                        Configuration
                    </h6>
                </div>
                <div class="card-body">
                    <!-- Role Selection -->
                    <div class="mb-3">
                        <label for="roleSelect" class="form-label">Select Role</label>
                        <select class="form-select" id="roleSelect" onchange="loadRole()">
                            <option value="">Choose a role...</option>
                        </select>
                    </div>
                    
                    <!-- Role Info -->
                    <div id="roleInfo" style="display: none;">
                        <div class="mb-3">
                            <label class="form-label">Role Details</label>
                            <div class="card bg-light">
                                <div class="card-body p-2">
                                    <h6 id="roleDisplayName" class="mb-1"></h6>
                                    <p id="roleDescription" class="text-muted small mb-2"></p>
                                    <div id="roleTools" class="d-flex flex-wrap gap-1">
                                        <!-- Tools badges will be added here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Configuration -->
                        <div class="mb-3">
                            <label for="temperature" class="form-label">Temperature</label>
                            <input type="range" class="form-range" id="temperature" 
                                   min="0" max="2" step="0.1" value="0.7">
                            <div class="d-flex justify-content-between">
                                <small>0.0</small>
                                <small id="temperatureValue">0.7</small>
                                <small>2.0</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="maxTokens" class="form-label">Max Tokens</label>
                            <input type="number" class="form-control" id="maxTokens" 
                                   min="1" max="4096" value="1000">
                        </div>
                    </div>
                    
                    <!-- Actions -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-success" onclick="startNewSession()" disabled id="startBtn">
                            <i class="fas fa-play me-1"></i>
                            Start Session
                        </button>
                        <button class="btn btn-warning" onclick="clearChat()" id="clearBtn">
                            <i class="fas fa-trash me-1"></i>
                            Clear Chat
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Session Info -->
            <div class="card mt-3" id="sessionCard" style="display: none;">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Session Info
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-1"><strong>Session ID:</strong></p>
                    <p class="text-muted small" id="sessionId">-</p>
                    <p class="mb-1"><strong>Messages:</strong></p>
                    <p class="text-muted small" id="messageCount">0</p>
                    <p class="mb-1"><strong>Started:</strong></p>
                    <p class="text-muted small" id="sessionStarted">-</p>
                    <p class="mb-1"><strong>Token Usage:</strong></p>
                    <div class="text-muted small" id="tokenUsage">
                        <div>Prompt tokens: <span id="promptTokens">0</span></div>
                        <div>Completion tokens: <span id="completionTokens">0</span></div>
                        <div>Total tokens: <span id="totalTokens">0</span></div>
                    </div>
                </div>
            </div>
            
            <!-- Session List -->
            <div class="card mt-3">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="card-title mb-0" style="cursor: pointer;" onclick="toggleSessionsList()" data-bs-toggle="collapse" data-bs-target="#sessionsListCollapse">
                            <i class="fas fa-history me-2"></i>
                            Recent Sessions
                            <i class="fas fa-chevron-down ms-2" id="sessionsCollapseIcon"></i>
                        </h6>
                        <button class="btn btn-sm btn-outline-primary" onclick="loadSessions()" id="refreshSessionsBtn">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="collapse show" id="sessionsListCollapse">
                    <div class="card-body p-0">
                        <div id="sessionsList" class="list-group list-group-flush">
                            <div class="list-group-item text-center text-muted py-3">
                                <i class="fas fa-clock mb-2"></i>
                                <div>No sessions found</div>
                                <small>Start a new session to see it here</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Chat Area -->
        <div class="col-md-9">
            <div class="card h-100">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-comments me-2"></i>
                            Chat with AI Agent
                            <span id="toolCallIndicator" class="badge bg-info ms-2" style="display: none;">
                                <i class="fas fa-cog fa-spin me-1"></i>Tool Calling
                            </span>
                        </h5>
                        <div id="statusIndicator" class="badge bg-secondary">
                            Not Connected
                        </div>
                    </div>
                </div>
                <div class="card-body d-flex flex-column p-0">
                    <!-- Chat Messages -->
                    <div class="chat-container flex-grow-1 p-3" id="chatContainer">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-robot fa-3x mb-3"></i>
                            <h5>Welcome to AI Agent Testing</h5>
                            <p>Select a role and start a session to begin chatting with the AI agent.</p>
                        </div>
                    </div>
                    
                    <!-- Chat Input -->
                    <div class="border-top p-3">
                        <!-- Role Switcher -->
                        <div class="mb-2" id="roleSwitcher" style="display: none;">
                            <div class="row g-2 align-items-center">
                                <div class="col-auto">
                                    <label for="currentRoleSwitch" class="form-label mb-0 small text-muted">Switch Role:</label>
                                </div>
                                <div class="col">
                                    <select class="form-select form-select-sm" id="currentRoleSwitch">
                                        <option value="">Select new role...</option>
                                    </select>
                                </div>
                                <div class="col-auto">
                                    <button class="btn btn-sm btn-outline-secondary" onclick="switchRole()" id="switchRoleBtn" disabled>
                                        <i class="fas fa-exchange-alt me-1"></i>Switch
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <form id="chatForm" onsubmit="sendMessage(event)">
                            <div class="input-group">
                                <input type="text" class="form-control" id="messageInput" 
                                       placeholder="Type your message..." disabled>
                                <button class="btn btn-primary" type="submit" disabled id="sendBtn">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Feedback Modal -->
<div class="modal fade" id="feedbackModal" tabindex="-1" aria-labelledby="feedbackModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="feedbackModalLabel">Submit Feedback</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="feedbackForm">
                    <input type="hidden" id="feedbackMessageId">
                    <input type="hidden" id="feedbackScore">
                    <div class="mb-3">
                        <label for="feedbackComment" class="form-label">Comment (optional)</label>
                        <textarea class="form-control" id="feedbackComment" rows="3"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Submit Feedback</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0" id="loadingMessage">Loading...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<!-- Markdown parsing library -->
<script src="https://cdn.jsdelivr.net/npm/marked@9.1.2/marked.min.js"></script>
<script>
let currentSession = null;
let currentRole = null;
let messageCount = 0;
let websocket = null;
let currentStreamingMessage = null;
let sessionTokenUsage = {prompt_tokens: 0, completion_tokens: 0, total_tokens: 0};

// Helper functions for token usage
function resetTokenUsage() {
    sessionTokenUsage = {prompt_tokens: 0, completion_tokens: 0, total_tokens: 0};
    document.getElementById('promptTokens').textContent = '0';
    document.getElementById('completionTokens').textContent = '0';
    document.getElementById('totalTokens').textContent = '0';
}

function extractTokenUsage(message) {
    if (message.prompt_tokens || message.completion_tokens || message.total_tokens) {
        return {
            prompt_tokens: message.prompt_tokens || 0,
            completion_tokens: message.completion_tokens || 0,
            total_tokens: message.total_tokens || 0
        };
    }
    return null;
}

function updateTokenUsage(tokenUsage) {
    if (!tokenUsage) return;
    sessionTokenUsage.prompt_tokens += tokenUsage.prompt_tokens || 0;
    sessionTokenUsage.completion_tokens += tokenUsage.completion_tokens || 0;
    sessionTokenUsage.total_tokens += tokenUsage.total_tokens || 0;
    
    document.getElementById('promptTokens').textContent = sessionTokenUsage.prompt_tokens.toString();
    document.getElementById('completionTokens').textContent = sessionTokenUsage.completion_tokens.toString();
    document.getElementById('totalTokens').textContent = sessionTokenUsage.total_tokens.toString();
}

function formatTokenUsage(tokenUsage) {
    if (!tokenUsage || (!tokenUsage.prompt_tokens && !tokenUsage.completion_tokens && !tokenUsage.total_tokens)) {
        return '';
    }
    return `<span class="message-tokens">${tokenUsage.total_tokens || 0} tokens</span>`;
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Customize marked to open links in a new tab
    const renderer = new marked.Renderer();
    renderer.link = function(href, title, text) {
        return `<a href="https://go.xapa.ai/#${href}" title="${title || ''}" target="_blank" rel="noopener noreferrer">${text}</a>`;
    };
    marked.setOptions({ renderer: renderer });

    loadRoles();
    loadSessions();
    setupEventListeners();
    
    // Check if role is pre-selected from URL
    const urlParams = new URLSearchParams(window.location.search);
    const selectedRole = urlParams.get('role');
    if (selectedRole) {
        document.getElementById('roleSelect').value = selectedRole;
        loadRole();
    }
});

// Setup event listeners
function setupEventListeners() {
    // Temperature slider
    const temperatureSlider = document.getElementById('temperature');
    temperatureSlider.addEventListener('input', function() {
        document.getElementById('temperatureValue').textContent = this.value;
    });
    
    // Role switcher dropdown
    const roleSwitchSelect = document.getElementById('currentRoleSwitch');
    roleSwitchSelect.addEventListener('change', function() {
        const switchBtn = document.getElementById('switchRoleBtn');
        if (this.value && this.value !== getCurrentRoleName()) {
            switchBtn.disabled = false;
        } else {
            switchBtn.disabled = true;
        }
    });
    
    // Sessions list collapse events
    const sessionsListCollapse = document.getElementById('sessionsListCollapse');
    sessionsListCollapse.addEventListener('show.bs.collapse', function() {
        const icon = document.getElementById('sessionsCollapseIcon');
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
    });
    
    sessionsListCollapse.addEventListener('hide.bs.collapse', function() {
        const icon = document.getElementById('sessionsCollapseIcon');
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
    });

    // Feedback form submission
    const feedbackForm = document.getElementById('feedbackForm');
    feedbackForm.addEventListener('submit', function(event) {
        event.preventDefault();
        submitFeedback();
    });
}

// Open feedback modal
function openFeedbackModal(messageId, score) {
    document.getElementById('feedbackMessageId').value = messageId;
    document.getElementById('feedbackScore').value = score;
    const feedbackModal = new bootstrap.Modal(document.getElementById('feedbackModal'));
    feedbackModal.show();
}

// Submit feedback
async function submitFeedback() {
    const messageId = document.getElementById('feedbackMessageId').value;
    const score = parseInt(document.getElementById('feedbackScore').value);
    const comment = document.getElementById('feedbackComment').value;

    try {
        await utils.apiPost('/feedback/', {
            message_id: messageId,
            score: score,
            comment: comment
        });
        utils.showAlert('Feedback submitted successfully!', 'success');
        const feedbackModal = bootstrap.Modal.getInstance(document.getElementById('feedbackModal'));
        feedbackModal.hide();
        document.getElementById('feedbackForm').reset();
    } catch (error) {
        console.error('Failed to submit feedback:', error);
        utils.showAlert('Failed to submit feedback: ' + error.message, 'danger');
    }
}

// Load available roles
async function loadRoles() {
    try {
        const response = await utils.apiGet('/roles/?active_only=false');""
        const roleSelect = document.getElementById('roleSelect');
        const roleSwitchSelect = document.getElementById('currentRoleSwitch');
        
        // Clear existing options except the first one
        roleSelect.innerHTML = '<option value="">Choose a role...</option>';
        roleSwitchSelect.innerHTML = '<option value="">Select new role...</option>';
        
        response.roles.forEach(role => {
            // Main role selector
            const option = document.createElement('option');
            option.value = role.name;
            option.textContent = role.display_name;
            roleSelect.appendChild(option);
            
            // Role switcher dropdown
            const switchOption = document.createElement('option');
            switchOption.value = role.name;
            switchOption.textContent = role.display_name;
            roleSwitchSelect.appendChild(switchOption);
        });
        
    } catch (error) {
        console.error('Failed to load roles:', error);
        utils.showAlert('Failed to load roles: ' + error.message, 'danger');
    }
}

// Load selected role
async function loadRole() {
    const roleName = document.getElementById('roleSelect').value;
    if (!roleName) {
        document.getElementById('roleInfo').style.display = 'none';
        document.getElementById('startBtn').disabled = true;
        return;
    }
    
    try {
        const response = await utils.apiGet(`/roles/${roleName}`);
        currentRole = response;
        
        // Update role info
        document.getElementById('roleDisplayName').textContent = response.display_name;
        document.getElementById('roleDescription').textContent = response.description || 'No description';
        
        // Update tools
        const toolsContainer = document.getElementById('roleTools');
        toolsContainer.innerHTML = response.tools.map(tool => 
            `<span class="badge bg-secondary">${utils.escapeHtml(tool)}</span>`
        ).join('');
        
        // Update configuration
        if (response.config) {
            document.getElementById('temperature').value = response.config.temperature || 0.7;
            document.getElementById('temperatureValue').textContent = response.config.temperature || 0.7;
            document.getElementById('maxTokens').value = response.config.max_tokens || 1000;
        }
        
        document.getElementById('roleInfo').style.display = 'block';
        document.getElementById('startBtn').disabled = false;
        
    } catch (error) {
        console.error('Failed to load role:', error);
        utils.showAlert('Failed to load role: ' + error.message, 'danger');
    }
}

// Load sessions
async function loadSessions() {
    try {
        const response = await utils.apiGet('/sessions/?limit=10');
        const sessionsList = document.getElementById('sessionsList');
        
        if (response.sessions && response.sessions.length > 0) {
            sessionsList.innerHTML = '';
            
            response.sessions.forEach(session => {
                const sessionItem = document.createElement('div');
                sessionItem.className = 'list-group-item d-flex justify-content-between align-items-start session-item-clickable';
                
                const roleInfo = session.role_name ? `<span class="badge bg-primary role-badge">${session.role_name}</span>` : '<span class="badge bg-secondary role-badge">Unknown Role</span>';
                const isActive = session.session_key === (currentSession?.session_key) ? 'border-primary' : '';
                
                sessionItem.innerHTML = `
                    <div class="w-100">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div class="flex-grow-1" style="cursor: pointer; min-width: 0;" onclick="resumeSession('${session.session_key}')">
                                <h6 class="mb-1 text-truncate session-name" title="${utils.escapeHtml(session.name)}">
                                    ${utils.escapeHtml(session.name)}
                                </h6>
                                <div class="d-flex align-items-center gap-2 mb-1">
                                    ${roleInfo}
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        ${utils.formatDate(session.created_at)}
                                    </small>
                                </div>
                            </div>
                            <div class="d-flex align-items-start ms-2 flex-shrink-0">
                                <button class="btn btn-sm btn-outline-danger delete-session-btn" onclick="event.stopPropagation(); deleteSession('${session.session_key}')" title="Delete Session">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                
                if (isActive) {
                    sessionItem.classList.add('border-primary');
                }
                
                sessionsList.appendChild(sessionItem);
            });
        } else {
            sessionsList.innerHTML = `
                <div class="list-group-item text-center text-muted py-3">
                    <i class="fas fa-clock mb-2"></i>
                    <div>No sessions found</div>
                    <small>Start a new session to see it here</small>
                </div>
            `;
        }
        
    } catch (error) {
        console.error('Failed to load sessions:', error);
        const sessionsList = document.getElementById('sessionsList');
        sessionsList.innerHTML = `
            <div class="list-group-item text-center text-danger py-3">
                <i class="fas fa-exclamation-triangle mb-2"></i>
                <div>Failed to load sessions</div>
                <small>${error.message}</small>
            </div>
        `;
    }
}

// Resume session
async function resumeSession(sessionKey) {
    try {
        utils.showLoading(true, 'Resuming session...');
        
        // Close existing WebSocket connection if any
        if (websocket) {
            websocket.close();
            websocket = null;
        }
        
        const response = await utils.apiGet(`/sessions/${sessionKey}`);
        currentSession = response;
        console.log('Session data:', response);
        
        // Load the role for this session
        if (response.role_name) {
            document.getElementById('roleSelect').value = response.role_name;
            await loadRole();
        }
        
        // Update UI
        document.getElementById('sessionId').textContent = response.id;
        document.getElementById('messageCount').textContent = response.message_count || 0;
        document.getElementById('sessionStarted').textContent = utils.formatDate(response.created_at);
        document.getElementById('sessionCard').style.display = 'block';
        document.getElementById('statusIndicator').textContent = 'Connected';
        document.getElementById('statusIndicator').className = 'badge bg-success';
        
        // Connect WebSocket for streaming with the new session
        await connectWebSocket();
        
        // Enable chat
        document.getElementById('messageInput').disabled = false;
        document.getElementById('sendBtn').disabled = false;
        
        // Show role switcher and update current role
        updateRoleSwitcher();
        
        // Clear chat and load session messages
        clearChatMessages();
        
        // Check if messages are already in session data
        if (response.messages && response.messages.length > 0) {
            console.log('Messages found in session data:', response.messages);
            loadMessagesFromData(response.messages);
        } else {
            console.log('No messages in session data, trying separate endpoint');
            await loadSessionMessages(sessionKey);
        }
        
        // Refresh sessions list to show active session
        await loadSessions();
        
        utils.showAlert('Session resumed successfully', 'success', 2000);
        
    } catch (error) {
        console.error('Failed to resume session:', error);
        utils.showAlert('Failed to resume session: ' + error.message, 'danger');
    } finally {
        utils.showLoading(false);
    }
}

// Load messages from session data
function loadMessagesFromData(messages) {
    try {
        console.log('Loading messages from session data:', messages);
        
        resetTokenUsage();
        
        if (messages && messages.length > 0) {
            addMessage('system', `Loading ${messages.length} previous messages...`, null, null);
            
            messages.forEach((message, index) => {
                const tokenUsage = extractTokenUsage(message);
                if (tokenUsage) updateTokenUsage(tokenUsage);
                
                if (message.role === 'user' || message.role === 'assistant') {
                    addMessage(message.role, message.content, message.metadata, tokenUsage);
                } else if (message.role === 'system') {
                    addMessage('system', message.content, null, null);
                }
            });
            
            messageCount = messages.length;
            document.getElementById('messageCount').textContent = messageCount.toString();
            addMessage('system', '--- Session resumed ---', null, null);
        } else {
            addMessage('system', 'Session resumed. No previous messages found.', null, null);
        }
        
    } catch (error) {
        console.error('Failed to load messages from data:', error);
        addMessage('system', `Failed to load message history: ${error.message}`, null, null);
    }
}

// Load session messages
async function loadSessionMessages(sessionKey) {
    try {
        resetTokenUsage();
        const response = await utils.apiGet(`/chat/${sessionKey}/history`);
        
        if (response.messages && response.messages.length > 0) {
            addMessage('system', `Loading ${response.messages.length} previous messages...`, null, null);
            
            response.messages.forEach((message) => {
                const tokenUsage = extractTokenUsage(message);
                if (tokenUsage) updateTokenUsage(tokenUsage);
                
                if (message.role === 'user' || message.role === 'assistant') {
                    addMessage(message.role, message.content, message.metadata, tokenUsage);
                } else if (message.role === 'system') {
                    addMessage('system', message.content, null, null);
                }
            });
            
            messageCount = response.messages.length;
            document.getElementById('messageCount').textContent = messageCount.toString();
            addMessage('system', '--- Session resumed ---', null, null);
        } else {
            addMessage('system', 'Session resumed. No previous messages found.', null, null);
        }
    } catch (error) {
        console.error('Failed to load session messages:', error);
        addMessage('system', `Session resumed, but failed to load previous messages: ${error.message}`, null, null);
    }
}

// Delete session
async function deleteSession(sessionKey) {
    const confirmed = confirm('Are you sure you want to delete this session? This action cannot be undone.');
    if (!confirmed) return;
    
    try {
        // Use fetch directly to handle empty responses properly
        const token = await utils.ensureAuthentication();
        const response = await fetch(`/api/v1/sessions/${sessionKey}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        // If we're deleting the current session, reset the UI
        if (currentSession && currentSession.session_key === sessionKey) {
            clearChat();
        }
        
        // Refresh sessions list
        await loadSessions();
        
        utils.showAlert('Session deleted successfully', 'success', 2000);
        
    } catch (error) {
        console.error('Failed to delete session:', error);
        utils.showAlert('Failed to delete session: ' + error.message, 'danger');
    }
}

// Toggle sessions list collapse
function toggleSessionsList() {
    // Bootstrap collapse will handle the animation and icon changes via event listeners
}

// Start new session
async function startNewSession() {
    if (!currentRole) return;
    
    try {
        utils.showLoading(true, 'Starting session...');
        
        // Close existing WebSocket connection if any
        if (websocket) {
            websocket.close();
            websocket = null;
        }
        
        // Debug: Check token before making request
        console.log('Before session creation:');
        console.log('- getAuthToken():', localStorage.getItem('api_token'));
        console.log('- Ensuring authentication...');
        
        // Ensure we have authentication before proceeding
        const token = await utils.ensureAuthentication();
        console.log('- Authentication token obtained:', token ? 'YES' : 'NO');
        
        const sessionData = {
            name: `Session - ${currentRole.display_name} - ${new Date().toLocaleString()}`,
            role_name: currentRole.name,
            config: {
                temperature: parseFloat(document.getElementById('temperature').value),
                max_tokens: parseInt(document.getElementById('maxTokens').value)
            }
        };
        
        console.log('Making API request to /sessions/ with token');
        const response = await utils.apiPost('/sessions/', sessionData);
        currentSession = response;
        messageCount = 0;
        
        // Update UI
        document.getElementById('sessionId').textContent = response.id;
        document.getElementById('messageCount').textContent = '0';
        document.getElementById('sessionStarted').textContent = utils.formatDate(response.created_at);
        document.getElementById('sessionCard').style.display = 'block';
        document.getElementById('statusIndicator').textContent = 'Connected';
        document.getElementById('statusIndicator').className = 'badge bg-success';
        
        // Connect WebSocket for streaming with the new session
        await connectWebSocket();

        // Enable chat
        document.getElementById('messageInput').disabled = false;
        document.getElementById('sendBtn').disabled = false;

        // Show role switcher and update current role
        updateRoleSwitcher();

        clearChatMessages();
        resetTokenUsage();
        addMessage('system', `Session started with role: ${currentRole.display_name} (Streaming enabled)`, null, null);
        
        // Refresh sessions list to show new session
        await loadSessions();

        utils.showAlert('Session started successfully with streaming', 'success', 2000);
        
    } catch (error) {
        console.error('Failed to start session:', error);
        utils.showAlert('Failed to start session: ' + error.message, 'danger');
    } finally {
        utils.showLoading(false);
    }
}

// WebSocket connection management
async function connectWebSocket() {
    if (!currentSession) {
        console.error('Cannot connect WebSocket: no current session');
        return;
    }

    // Close existing connection if any
    if (websocket && websocket.readyState === WebSocket.OPEN) {
        websocket.close();
        websocket = null;
    }

    try {
        const authToken = await utils.ensureAuthentication();
        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${wsProtocol}//${window.location.host}/ws/chat?session_key=${currentSession.session_key}&token=${authToken}&x_client_id=06780bed-419b-7fae-8000-15044017599a`;
        
        websocket = new WebSocket(wsUrl);

        websocket.onopen = function(event) {
            console.log('WebSocket connected');
            document.getElementById('statusIndicator').textContent = 'Connected (Streaming)';
            document.getElementById('statusIndicator').className = 'badge bg-success';
        };

        websocket.onmessage = function(event) {
            handleWebSocketMessage(JSON.parse(event.data));
        };

        websocket.onclose = function(event) {
            console.log('WebSocket disconnected');
            document.getElementById('statusIndicator').textContent = 'Disconnected';
            document.getElementById('statusIndicator').className = 'badge bg-danger';
            websocket = null;
        };

        websocket.onerror = function(error) {
            console.error('WebSocket error:', error);
            addMessage('system', 'WebSocket connection error. Please try again.', null, null);
        };

    } catch (error) {
        console.error('Failed to connect WebSocket:', error);
        utils.showAlert('Failed to connect WebSocket: ' + error.message, 'danger');
    }
}

function handleWebSocketMessage(data) {
    switch (data.type) {
        case 'connection.established':
            console.log('WebSocket connection established');
            break;

        case 'chat.message.start':
            // Start a new streaming message
            currentStreamingMessage = {
                id: data.message_id,
                content: '',
                element: null,
                tool_calls: [],
                toolCallsInserted: false
            };
            // Don't add the assistant message yet - wait for tool calls or first content
            break;
            
        case 'tool.call':
            // Handle tool call information
            if (currentStreamingMessage && data.message_id === currentStreamingMessage.id) {
                // Show tool calling indicator
                document.getElementById('toolCallIndicator').style.display = 'inline-block';
                
                // Store tool call info
                currentStreamingMessage.tool_calls.push({
                    name: data.tool_name,
                    arguments: data.arguments,
                    result: data.result
                });
                
                // Add tool call message
                addToolCallMessage(data.tool_name, data.arguments, data.result);
            }
            break;

        case 'tool.call.start':
            // Handle tool call start
            if (currentStreamingMessage && data.message_id === currentStreamingMessage.id) {
                // Show tool calling indicator
                document.getElementById('toolCallIndicator').style.display = 'inline-block';
                
                // Add tool call start message
                addToolCallStartMessage(data.metadata.tool_name, data.metadata.arguments);
                currentStreamingMessage.toolCallsInserted = true;
            }
            break;
            
        case 'tool.call.result':
            // Handle tool call result
            if (currentStreamingMessage && data.message_id === currentStreamingMessage.id) {
                // Update the tool call message with result
                updateToolCallResult(data.metadata.tool_name, data.metadata.result);
            }
            break;
            
        case 'tool.call.error':
            // Handle tool call error
            if (currentStreamingMessage && data.message_id === currentStreamingMessage.id) {
                // Update the tool call message with error
                updateToolCallError(data.metadata.tool_name, data.metadata.error);
            }
            break;

        case 'chat.message.chunk':
            // Append chunk to current streaming message
            if (currentStreamingMessage && data.message_id === currentStreamingMessage.id) {
                // If this is the first content and we haven't added the assistant message yet, add it now
                if (!currentStreamingMessage.element && data.content) {
                    addStreamingMessage('assistant', '');
                }
                
                currentStreamingMessage.content += data.content || '';
                updateStreamingMessage(currentStreamingMessage.content);
            }
            break;

        case 'chat.message.end':
            if (currentStreamingMessage && data.message_id === currentStreamingMessage.id) {
                const tokenUsage = data.metadata && data.metadata.usage ? data.metadata.usage : null;
                
                if (!currentStreamingMessage.element) {
                    addStreamingMessage('assistant', currentStreamingMessage.content || 'Tool execution completed.', tokenUsage);
                }
                
                finalizeStreamingMessage(tokenUsage);
                messageCount += 2;
                document.getElementById('messageCount').textContent = messageCount.toString();
                
                if (tokenUsage) updateTokenUsage(tokenUsage);
                
                document.getElementById('toolCallIndicator').style.display = 'none';
                currentStreamingMessage = null;
            }
            break;

        case 'error':
            console.error('WebSocket error:', data.metadata?.error);
            addMessage('system', `Error: ${data.metadata?.error || 'Unknown error'}`, null, null);
            break;

        case 'role.switched':
            // Handle role switch notification
            console.log('Role switched:', data.metadata);
            addMessage('system', `Role switched from ${data.metadata.previous_role} to ${data.metadata.new_role}`, null, null);
            
            // Update current session role info
            if (currentSession) {
                currentSession.role_name = data.metadata.new_role;
            }
            
            // Update role switcher
            updateRoleSwitcher();
            
            // Re-enable switch button
            const switchBtn = document.getElementById('switchRoleBtn');
            switchBtn.disabled = false;
            switchBtn.innerHTML = '<i class="fas fa-exchange-alt me-1"></i>Switch';
            
            utils.showAlert(`Role switched to ${data.metadata.new_role}`, 'success', 3000);
            break;

        case 'pong':
            // Handle ping/pong for connection keep-alive
            break;

        default:
            console.log('Unknown WebSocket message type:', data.type);
    }
}

// Send message via WebSocket
async function sendMessage(event) {
    event.preventDefault();

    const input = document.getElementById('messageInput');
    const message = input.value.trim();

    if (!message || !currentSession) return;

    // Ensure WebSocket is connected
    if (!websocket || websocket.readyState !== WebSocket.OPEN) {
        await connectWebSocket();
        // Wait a bit for connection to establish
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    if (!websocket || websocket.readyState !== WebSocket.OPEN) {
        addMessage('system', 'Error: WebSocket not connected. Please try again.', null, null);
        return;
    }

    // Add user message to chat (user messages don't have token usage from LLM)
    addMessage('user', message, null, null);
    input.value = '';

    // Disable input while processing
    input.disabled = true;
    document.getElementById('sendBtn').disabled = true;

    try {
        // Send message via WebSocket
        const messageData = {
            type: 'chat.message',
            content: message
        };

        websocket.send(JSON.stringify(messageData));

    } catch (error) {
        console.error('Failed to send message:', error);
        addMessage('system', 'Error: Failed to send message. Please try again.', null, null);
        utils.showAlert('Failed to send message: ' + error.message, 'danger');
    } finally {
        // Re-enable input
        input.disabled = false;
        document.getElementById('sendBtn').disabled = false;
        input.focus();
    }
}

// Add message to chat
function addMessage(type, content, metadata = null, tokenUsage = null) {
    const container = document.getElementById('chatContainer');

    // Remove welcome message if it exists
    const welcomeMessage = container.querySelector('.text-center.text-muted');
    if (welcomeMessage) {
        welcomeMessage.remove();
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    if (metadata && metadata.message_id) {
        messageDiv.dataset.messageId = metadata.message_id;
    }
    
    // Parse markdown for assistant messages, escape HTML for others
    let messageContent;
    if (type === 'assistant') {
        messageContent = `<div class="message-content">${marked.parse(content)}</div>`;
    } else {
        messageContent = `<div class="message-content">${utils.escapeHtml(content)}</div>`;
    }
    
    // Add tool call information if present
    if (metadata && metadata.tool_calls) {
        messageContent += '<div class="tool-call-info"><strong>Tool Calls:</strong><br>';
        metadata.tool_calls.forEach(toolCall => {
            messageContent += `🔧 ${toolCall.function.name}(${toolCall.function.arguments})<br>`;
        });
        messageContent += '</div>';
    }
    
    const tokenDisplay = formatTokenUsage(tokenUsage);
    
    let feedbackButtons = '';
    if (type === 'assistant' && metadata && metadata.message_id) {
        feedbackButtons = `
            <div class="feedback-buttons">
                <button class="btn btn-sm btn-outline-success" onclick="openFeedbackModal('${metadata.message_id}', 1)"><i class="fas fa-thumbs-up"></i></button>
                <button class="btn btn-sm btn-outline-danger" onclick="openFeedbackModal('${metadata.message_id}', -1)"><i class="fas fa-thumbs-down"></i></button>
            </div>
        `;
    }

    messageDiv.innerHTML = `
        ${messageContent}
        <div class="message-time">
            <span>${new Date().toLocaleTimeString()}</span>
            ${tokenDisplay}
            ${feedbackButtons}
        </div>
    `;

    container.appendChild(messageDiv);
    container.scrollTop = container.scrollHeight;
}

// Add tool call start message (shows execution in progress)
function addToolCallStartMessage(toolName, arguments_str) {
    const container = document.getElementById('chatContainer');
    
    const toolDiv = document.createElement('div');
    toolDiv.className = 'message tool';
    toolDiv.id = `tool-${toolName}-${Date.now()}`; // Unique ID for updating
    
    const uniqueId = `tool-content-${Date.now()}`;
    
    toolDiv.innerHTML = `
        <div class=\"tool-indicator\">
            <i class=\"fas fa-cog fa-spin me-1\"></i>Executing Tool: ${utils.escapeHtml(toolName)}
        </div>
        <div class=\"tool-call-info\">
            <div class=\"tool-call-header\" onclick=\"toggleToolCall('${uniqueId}')\">
                <strong>Tool Details</strong>
                <i class=\"fas fa-chevron-down collapse-icon collapsed\" id=\"icon-${uniqueId}\"></i>
            </div>
            <div class=\"tool-call-content collapsed\" id=\"${uniqueId}\">
                <strong>Arguments:</strong><br>
                <code>${utils.escapeHtml(arguments_str || '{}')}</code><br><br>
                <div class=\"text-info\">
                    <i class=\"fas fa-spinner fa-spin me-1\"></i>Executing...
                </div>
            </div>
        </div>
        <div class=\"message-time\">${new Date().toLocaleTimeString()}</div>
    `;
    
    container.appendChild(toolDiv);
    container.scrollTop = container.scrollHeight;
    
    return toolDiv.id;
}

// Update tool call with result
function updateToolCallResult(toolName, result) {
    // Find the most recent tool call div for this tool
    const toolDivs = document.querySelectorAll('.message.tool');
    let targetDiv = null;
    
    for (let i = toolDivs.length - 1; i >= 0; i--) {
        const div = toolDivs[i];
        if (div.innerHTML.includes(`Executing Tool: ${utils.escapeHtml(toolName)}`)) {
            targetDiv = div;
            break;
        }
    }
    
    if (targetDiv) {
        const resultText = typeof result === 'object' ? JSON.stringify(result, null, 2) : result;
        
        // Update the tool indicator to show completion
        const indicator = targetDiv.querySelector('.tool-indicator');
        if (indicator) {
            indicator.innerHTML = `
                <i class=\"fas fa-check-circle text-success me-1\"></i>Tool Completed: ${utils.escapeHtml(toolName)}
            `;
        }
        
        // Update the result section
        const contentDiv = targetDiv.querySelector('.tool-call-content');
        if (contentDiv) {
            const args = contentDiv.querySelector('code').textContent;
            const uniqueId = contentDiv.id;
            contentDiv.innerHTML = `
                <strong>Arguments:</strong><br>
                <code>${utils.escapeHtml(args)}</code><br><br>
                <strong>Result:</strong><br>
                <pre class=\"text-success\">${utils.escapeHtml(resultText)}</pre>
            `;
        }
    }
    
    const container = document.getElementById('chatContainer');
    container.scrollTop = container.scrollHeight;
}

// Update tool call with error
function updateToolCallError(toolName, error) {
    // Find the most recent tool call div for this tool
    const toolDivs = document.querySelectorAll('.message.tool');
    let targetDiv = null;
    
    for (let i = toolDivs.length - 1; i >= 0; i--) {
        const div = toolDivs[i];
        if (div.innerHTML.includes(`Executing Tool: ${utils.escapeHtml(toolName)}`)) {
            targetDiv = div;
            break;
        }
    }
    
    if (targetDiv) {
        // Update the tool indicator to show error
        const indicator = targetDiv.querySelector('.tool-indicator');
        if (indicator) {
            indicator.innerHTML = `
                <i class=\"fas fa-exclamation-triangle text-danger me-1\"></i>Tool Error: ${utils.escapeHtml(toolName)}
            `;
        }
        
        // Update the result section
        const contentDiv = targetDiv.querySelector('.tool-call-content');
        if (contentDiv) {
            const args = contentDiv.querySelector('code').textContent;
            contentDiv.innerHTML = `
                <strong>Arguments:</strong><br>
                <code>${utils.escapeHtml(args)}</code><br><br>
                <strong>Error:</strong><br>
                <pre class=\"text-danger\">${utils.escapeHtml(error)}</pre>
            `;
        }
    }
    
    const container = document.getElementById('chatContainer');
    container.scrollTop = container.scrollHeight;
}

// Add tool call message to chat
function addToolCallMessage(toolName, arguments_str, result) {
    const container = document.getElementById('chatContainer');
    
    const toolDiv = document.createElement('div');
    toolDiv.className = 'message tool';
    
    const resultText = typeof result === 'object' ? JSON.stringify(result, null, 2) : result;
    const uniqueId = `tool-content-${Date.now()}`;
    
    toolDiv.innerHTML = `
        <div class="tool-indicator">
            <i class="fas fa-check-circle text-success me-1"></i>Tool Call: ${utils.escapeHtml(toolName)}
        </div>
        <div class="tool-call-info">
            <div class="tool-call-header" onclick="toggleToolCall('${uniqueId}')">
                <strong>Tool Details</strong>
                <i class="fas fa-chevron-down collapse-icon collapsed" id="icon-${uniqueId}"></i>
            </div>
            <div class="tool-call-content collapsed" id="${uniqueId}">
                <strong>Arguments:</strong><br>
                <code>${utils.escapeHtml(arguments_str || '{}')}</code><br><br>
                <strong>Result:</strong><br>
                <pre class="text-success">${utils.escapeHtml(resultText)}</pre>
            </div>
        </div>
        <div class="message-time">${new Date().toLocaleTimeString()}</div>
    `;
    
    container.appendChild(toolDiv);
    container.scrollTop = container.scrollHeight;
}

// Toggle tool call content visibility
function toggleToolCall(contentId) {
    const content = document.getElementById(contentId);
    const icon = document.getElementById(`icon-${contentId}`);
    
    if (content && icon) {
        content.classList.toggle('collapsed');
        icon.classList.toggle('collapsed');
    }
}

// Add streaming message placeholder
function addStreamingMessage(type, content, tokenUsage = null) {
    const container = document.getElementById('chatContainer');

    // Remove welcome message if it exists
    const welcomeMessage = container.querySelector('.text-center.text-muted');
    if (welcomeMessage) {
        welcomeMessage.remove();
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type} streaming`;
    
    // Parse markdown for assistant messages, escape HTML for others
    let messageContent;
    if (type === 'assistant') {
        messageContent = `<div class="message-content">${marked.parse(content)}<span class="typing-indicator">▋</span></div>`;
    } else {
        messageContent = `<div class="message-content">${utils.escapeHtml(content)}<span class="typing-indicator">▋</span></div>`;
    }
    
    // Create token usage display (initially empty for streaming messages)
    const tokenDisplay = formatTokenUsage(tokenUsage);
    
    messageDiv.innerHTML = `
        ${messageContent}
        <div class="message-time">
            <span>${new Date().toLocaleTimeString()}</span>
            <span class="message-tokens-placeholder">${tokenDisplay}</span>
        </div>
    `;

    container.appendChild(messageDiv);
    container.scrollTop = container.scrollHeight;

    // Store reference for streaming updates
    if (currentStreamingMessage) {
        currentStreamingMessage.element = messageDiv;
    }
}

// Update streaming message content
function updateStreamingMessage(content) {
    if (currentStreamingMessage && currentStreamingMessage.element) {
        const contentDiv = currentStreamingMessage.element.querySelector('.message-content');
        if (contentDiv) {
            // Parse markdown for assistant messages
            const messageType = currentStreamingMessage.element.classList.contains('assistant') ? 'assistant' : 'user';
            if (messageType === 'assistant') {
                contentDiv.innerHTML = `${marked.parse(content)}<span class="typing-indicator">▋</span>`;
            } else {
                contentDiv.innerHTML = `${utils.escapeHtml(content)}<span class="typing-indicator">▋</span>`;
            }
            // Auto-scroll to bottom
            const container = document.getElementById('chatContainer');
            container.scrollTop = container.scrollHeight;
        }
    }
}

// Finalize streaming message
function finalizeStreamingMessage(tokenUsage = null) {
    console.log('finalizeStreamingMessage called with tokenUsage:', tokenUsage);
    if (currentStreamingMessage && currentStreamingMessage.element) {
        currentStreamingMessage.element.dataset.messageId = currentStreamingMessage.id;
        const contentDiv = currentStreamingMessage.element.querySelector('.message-content');
        if (contentDiv) {
            // Parse markdown for assistant messages, escape HTML for others
            const messageType = currentStreamingMessage.element.classList.contains('assistant') ? 'assistant' : 'user';
            if (messageType === 'assistant') {
                contentDiv.innerHTML = marked.parse(currentStreamingMessage.content);
            } else {
                contentDiv.innerHTML = utils.escapeHtml(currentStreamingMessage.content);
            }
            currentStreamingMessage.element.classList.remove('streaming');
            
            // Add feedback buttons for assistant messages
            if (messageType === 'assistant') {
                const feedbackContainer = document.createElement('div');
                feedbackContainer.className = 'feedback-buttons';
                feedbackContainer.innerHTML = `
                    <button class="btn btn-sm btn-outline-success" onclick="openFeedbackModal('${currentStreamingMessage.id}', 1)"><i class="fas fa-thumbs-up"></i></button>
                    <button class="btn btn-sm btn-outline-danger" onclick="openFeedbackModal('${currentStreamingMessage.id}', -1)"><i class="fas fa-thumbs-down"></i></button>
                `;
                currentStreamingMessage.element.querySelector('.message-time').appendChild(feedbackContainer);
            }

            // Update token usage if provided
            const tokenHTML = formatTokenUsage(tokenUsage);
            if (tokenHTML) {
                console.log('Trying to update token usage in streaming message');
                
                // Try to find the token placeholder first
                const tokenPlaceholder = currentStreamingMessage.element.querySelector('.message-tokens-placeholder');
                console.log('Found token placeholder element:', tokenPlaceholder);
                
                if (tokenPlaceholder) {
                    tokenPlaceholder.innerHTML = tokenHTML;
                    console.log('Updated token placeholder with:', tokenHTML);
                } else {
                    console.log('Token placeholder not found, trying alternative approach');
                    // Alternative approach - find the message-time div and add token display
                    const messageTimeDiv = currentStreamingMessage.element.querySelector('.message-time');
                    if (messageTimeDiv) {
                        // Get the existing content and add token display
                        const timeSpan = messageTimeDiv.querySelector('span');
                        if (timeSpan) {
                            messageTimeDiv.innerHTML = `<span>${timeSpan.textContent}</span>${tokenHTML}`;
                            console.log('Added token display to message-time div:', tokenHTML);
                        }
                    }
                }
            } else {
                console.log('No valid token usage provided to finalizeStreamingMessage');
            }
        }
    } else {
        console.log('No currentStreamingMessage or element to finalize');
    }
}

// Clear chat messages
function clearChatMessages() {
    const container = document.getElementById('chatContainer');
    container.innerHTML = '';
}

// Clear chat and reset session
function clearChat() {
    const confirmed = confirm('Are you sure you want to clear the chat history?');
    if (confirmed) {
        clearChatMessages();
        messageCount = 0;
        document.getElementById('messageCount').textContent = '0';
        
        // Reset token usage counters
        sessionTokenUsage = {
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0
        };
        // Reset display
        document.getElementById('promptTokens').textContent = '0';
        document.getElementById('completionTokens').textContent = '0';
        document.getElementById('totalTokens').textContent = '0';

        // Close WebSocket connection
        if (websocket) {
            websocket.close();
            websocket = null;
        }

        // Reset session
        currentSession = null;
        currentStreamingMessage = null;

        // Update UI
        document.getElementById('sessionCard').style.display = 'none';
        document.getElementById('roleSwitcher').style.display = 'none';
        document.getElementById('messageInput').disabled = true;
        document.getElementById('sendBtn').disabled = true;
        document.getElementById('toolCallIndicator').style.display = 'none';

        // Refresh sessions list
        loadSessions();

        utils.showAlert('Chat cleared', 'info', 2000);
    }
}

// Get current role name
function getCurrentRoleName() {
    return currentSession?.role_name || (currentRole?.name);
}

// Helper function to format token usage for display
function formatTokenUsage(tokenUsage) {
    if (!tokenUsage || typeof tokenUsage !== 'object') {
        return '';
    }
    
    // Prioritize showing total tokens first, then breakdown if needed
    if (tokenUsage.total_tokens && tokenUsage.total_tokens > 0) {
        let display = `${tokenUsage.total_tokens.toLocaleString()} tokens`;
        
        // Add breakdown for messages with significant token usage
        if (tokenUsage.total_tokens > 100) {
            const breakdown = [];
            if (tokenUsage.prompt_tokens && tokenUsage.prompt_tokens > 0) {
                breakdown.push(`${tokenUsage.prompt_tokens.toLocaleString()} in`);
            }
            if (tokenUsage.completion_tokens && tokenUsage.completion_tokens > 0) {
                breakdown.push(`${tokenUsage.completion_tokens.toLocaleString()} out`);
            }
            if (breakdown.length > 0) {
                display = `${tokenUsage.total_tokens.toLocaleString()} tokens (${breakdown.join(' + ')})`;
            }
        }
        
        return `<span class="message-tokens">${display}</span>`;
    }
    
    return '';
}

// Update token usage display
function updateTokenUsage(usage) {
    console.log('updateTokenUsage called with:', usage);
    if (usage && typeof usage === 'object') {
        // Add to session totals
        sessionTokenUsage.prompt_tokens += usage.prompt_tokens || 0;
        sessionTokenUsage.completion_tokens += usage.completion_tokens || 0;
        sessionTokenUsage.total_tokens += usage.total_tokens || 0;
        
        // Update display
        document.getElementById('promptTokens').textContent = sessionTokenUsage.prompt_tokens.toLocaleString();
        document.getElementById('completionTokens').textContent = sessionTokenUsage.completion_tokens.toLocaleString();
        document.getElementById('totalTokens').textContent = sessionTokenUsage.total_tokens.toLocaleString();
        
        console.log('Updated session totals:', sessionTokenUsage);
    }
}

// Update role switcher visibility and options
function updateRoleSwitcher() {
    const roleSwitcher = document.getElementById('roleSwitcher');
    const roleSwitchSelect = document.getElementById('currentRoleSwitch');
    const switchBtn = document.getElementById('switchRoleBtn');
    
    if (currentSession) {
        // Show role switcher
        roleSwitcher.style.display = 'block';
        
        // Reset dropdown
        roleSwitchSelect.value = '';
        switchBtn.disabled = true;
        
        // Update dropdown options to exclude current role
        const currentRoleName = getCurrentRoleName();
        const options = roleSwitchSelect.querySelectorAll('option');
        options.forEach(option => {
            if (option.value === currentRoleName) {
                option.style.display = 'none';
            } else {
                option.style.display = 'block';
            }
        });
    } else {
        // Hide role switcher
        roleSwitcher.style.display = 'none';
    }
}

// Switch role function
async function switchRole() {
    const newRoleName = document.getElementById('currentRoleSwitch').value;
    const currentRoleName = getCurrentRoleName();
    
    if (!newRoleName || !currentSession || newRoleName === currentRoleName) {
        return;
    }
    
    try {
        // Disable the button during switching
        const switchBtn = document.getElementById('switchRoleBtn');
        switchBtn.disabled = true;
        switchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Switching...';
        
        // Send role switch message via WebSocket
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            const switchMessage = {
                type: 'role.switch',
                role_name: newRoleName
            };
            
            websocket.send(JSON.stringify(switchMessage));
            utils.showAlert(`Switching role to ${newRoleName}...`, 'info', 2000);
        } else {
            throw new Error('WebSocket not connected');
        }
        
    } catch (error) {
        console.error('Failed to switch role:', error);
        utils.showAlert('Failed to switch role: ' + error.message, 'danger');
        
        // Re-enable button
        const switchBtn = document.getElementById('switchRoleBtn');
        switchBtn.disabled = false;
        switchBtn.innerHTML = '<i class="fas fa-exchange-alt me-1"></i>Switch';
    }
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (websocket) {
        websocket.close();
    }
});

</script>
{% endblock %}
