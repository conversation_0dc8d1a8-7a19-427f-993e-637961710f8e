# Folder Structure Documentation

This document provides an overview of the project's folder structure and explains the purpose of each directory and file.

## Root Directory

```
xapa_ai/
├── alembic/                 # Database migration files
├── app/                     # Main application code
├── config/                  # Configuration files
├── docs/                    # Documentation files
├── roles/                   # Role definition files
├── static/                  # Static assets (CSS, JS, images)
├── templates/               # HTML templates
├── .env.example            # Environment variable examples
├── .gitignore              # Git ignore file
├── alembic.ini             # Alembic configuration
├── docker-compose.yml      # Docker Compose configuration
├── Dockerfile              # Docker configuration
├── README.md               # Project README
└── requirements.txt        # Python dependencies
```

## Alembic Directory

```
alembic/
├── versions/               # Migration scripts
│   ├── 001_add_image_url_to_roles.py
│   └── 002_add_image_url_to_roles.py
├── env.py                  # Alembic environment configuration
├── README                  # Alembic README
└── script.py.mako          # Migration script template
```

## App Directory

```
app/
├── agent/                  # AI agent system
│   ├── tools/              # AI agent tools
│   │   ├── base.py         # Base tool classes
│   │   ├── code_tools.py   # Code execution tools
│   │   ├── content_database_tools.py  # Content database tools
│   │   ├── health_tools.py # Health check tools
│   │   ├── knowledge_tools.py         # Knowledge base tools
│   │   ├── pinecone_tools.py          # Pinecone vector database tools
│   │   ├── sample_tools.py # Sample tools for demonstration
│   │   ├── tool_manager.py # Tool management system
│   │   ├── user_database_tools.py     # User database tools
│   │   └── writing_tools.py           # Writing assistance tools
│   ├── factory.py          # Agent factory
│   └── role_loader.py      # Role configuration loader
├── api/                    # API endpoints
│   ├── auth.py             # Authentication endpoints
│   ├── chat.py             # Chat endpoints
│   ├── embedding.py        # Embedding endpoints
│   ├── roles.py            # Role management endpoints
│   ├── session.py          # Session management endpoints
│   ├── tools.py            # Tool management endpoints
│   ├── web.py              # Web interface routes
│   └── websocket.py        # WebSocket handlers
├── core/                   # Core functionality
│   ├── config.py           # Configuration management
│   ├── dependencies.py     # FastAPI dependencies
│   ├── exceptions.py       # Custom exception classes
│   └── security.py         # Security utilities
├── embedding/              # Embedding provider abstraction
│   ├── base.py             # Base embedding provider interface
│   ├── openai_provider.py  # OpenAI embedding provider
│   ├── azure_openai_provider.py       # Azure OpenAI embedding provider
│   ├── provider_factory.py # Embedding provider factory
│   └── service.py          # Embedding service logic
├── llm/                    # LLM provider abstraction
│   ├── base.py             # Base LLM provider interface
│   ├── openai_provider.py  # OpenAI LLM provider
│   ├── azure_openai_provider.py       # Azure OpenAI LLM provider
│   └── provider_factory.py # LLM provider factory
├── models/                 # SQLAlchemy ORM models
│   ├── base.py             # Database engine and base class
│   ├── user.py             # User model
│   ├── session.py          # Session model
│   ├── message.py          # Message model
│   └── role.py             # Role model
├── schemas/                # Pydantic schemas for API validation
│   ├── auth.py             # Authentication schemas
│   ├── chat.py             # Chat schemas
│   ├── embedding.py        # Embedding schemas
│   ├── role.py             # Role schemas
│   ├── session.py          # Session schemas
│   └── tool.py             # Tool schemas
├── services/               # Business logic services
│   ├── auth0_service.py    # Auth0 integration service
│   ├── azure_storage.py    # Azure Blob Storage service
│   └── summarization_service.py   # Text summarization service
├── session/                # Session management
│   ├── manager.py          # Session business logic
│   └── storage.py          # Session storage (Redis)
└── utils/                  # Utilities
    └── database.py         # Database utilities
```

## Config Directory

```
config/
└── agent.xapa.ai.conf      # Nginx configuration
```

## Docs Directory

```
docs/
├── architecture/           # Architecture documentation
│   ├── README.md           # Architecture documentation index
│   ├── folder_structure.md # This file
│   ├── llm_provider_template.md    # LLM provider template
│   ├── tool_template.md    # Tool implementation template
│   ├── schema_template.md  # Schema implementation template
│   ├── embedding_provider_template.md  # Embedding provider template
│   └── api_endpoint_template.md    # API endpoint template
├── design/                 # Design documents
└── features/               # Feature documentation
```

## Roles Directory

```
roles/
└── xappy.md                # Default role definition
```

## Static Directory

```
static/
├── css/                    # CSS stylesheets
│   └── style.css           # Main stylesheet
├── files/                  # Static files
│   └── pinecone_tool_msg.json     # Sample file for Pinecone tool
├── images/                 # Image assets
└── js/                     # JavaScript files
    ├── main.js             # Main JavaScript
    ├── role-form.js        # Role form JavaScript
    └── roles.js            # Roles page JavaScript
```

## Templates Directory

```
templates/
├── auth/                   # Authentication templates
│   ├── login.html          # Login page
│   └── profile.html        # User profile page
├── roles/                  # Role management templates
│   ├── form.html           # Role form template
│   └── list.html           # Role list template
├── test/                   # Test templates
│   └── agent.html          # Agent test page
├── tools/                  # Tool templates
│   └── list.html           # Tool list template
├── base.html               # Base template
└── home.html               # Home page template